{"name": "compex", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "seed": "ts-node prisma/seed.ts", "analyze": "ANALYZE=true next build", "analyze-bundle": "tsx scripts/analyze-bundle.ts", "optimize-db": "tsx scripts/optimize-database.ts", "perf-report": "tsx scripts/analyze-bundle.ts && tsx scripts/optimize-database.ts"}, "dependencies": {"@next/font": "^14.2.4", "@prisma/client": "^5.17.0", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@reduxjs/toolkit": "^2.2.6", "@types/pg": "^8.11.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "framer-motion": "^12.17.0", "katex": "^0.16.11", "lucide-react": "^0.484.0", "moment": "^2.30.1", "next": "14.2.5", "next-themes": "^0.4.6", "openai": "^4.0.0", "pg": "^8.12.0", "prisma": "^5.17.0", "react": "^18", "react-dom": "^18", "react-katex": "^3.0.1", "react-mathjax2": "^0.0.2", "react-redux": "^9.1.2", "recharts": "^2.15.1", "redis": "^4.7.0", "sharp": "^0.33.5", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "web-vitals": "^5.0.3"}, "devDependencies": {"@next/bundle-analyzer": "^15.3.3", "@types/katex": "^0.16.7", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-katex": "^3.0.4", "eslint": "^8", "eslint-config-next": "14.2.4", "postcss": "^8", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "tsx": "^4.20.3", "typescript": "^5.5.3", "webpack-bundle-analyzer": "^4.10.2"}}