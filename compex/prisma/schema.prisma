generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model examtypes {
  examtypeid  Int           @id @default(autoincrement())
  name        String        @db.VarChar(100)
  description String?
  ProblemsSet ProblemsSet[]
  mocktests   mocktests[]
  problems    problems[]
  sections    sections[]
  tags        tags[]
}

model mocktestrankings {
  rankingid  Int        @id @default(autoincrement())
  userid     Int?
  mocktestid Int?
  rank       Int?
  percentile Decimal?   @db.Decimal(5, 2)
  mocktests  mocktests? @relation(fields: [mocktestid], references: [mocktestid], onDelete: NoAction, onUpdate: NoAction)
  users      users?     @relation(fields: [userid], references: [userid], onDelete: NoAction, onUpdate: NoAction)
}

model mocktests {
  mocktestid           Int                    @id @default(autoincrement())
  difficulty           Int                    @default(0)
  examtypeid           Int?
  date                 DateTime               @db.Date
  starttime            DateTime               @db.Time(6)
  endtime              DateTime               @db.Time(6)
  isactive             Boolean?               @default(true)
  mocksections         mocksections[]
  mocktestrankings     mocktestrankings[]
  examtypes            examtypes?             @relation(fields: [examtypeid], references: [examtypeid], onDelete: NoAction, onUpdate: NoAction)
  usermocktestattempts usermocktestattempts[]
}

model mocktestsectionscores {
  scoreid              Int                   @id @default(autoincrement())
  attemptid            Int?
  sectionid            Int?
  score                Decimal?              @db.Decimal(5, 2)
  usermocktestattempts usermocktestattempts? @relation(fields: [attemptid], references: [attemptid], onDelete: NoAction, onUpdate: NoAction)
  sections             sections?             @relation(fields: [sectionid], references: [sectionid], onDelete: NoAction, onUpdate: NoAction)
}

model performance {
  userid              Int
  sectionid           Int
  correctcount        Int?                     @default(0)
  incorrectcount      Int?                     @default(0)
  partialcorrectcount Int?                     @default(0)
  averagespeed        Unsupported("interval")?
  sections            sections                 @relation(fields: [sectionid], references: [sectionid], onDelete: NoAction, onUpdate: NoAction)
  users               users                    @relation(fields: [userid], references: [userid], onDelete: NoAction, onUpdate: NoAction)

  @@id([userid, sectionid])
}

model problemoptions {
  optionid                    Int                           @id @default(autoincrement())
  problemid                   Int?
  optiontext                  String
  iscorrect                   Boolean?
  group                       String?
  problems                    problems?                     @relation(fields: [problemid], references: [problemid], onDelete: NoAction, onUpdate: NoAction)
  userattempt_selectedoptions userattempt_selectedoptions[]
}

model problems {
  type                 String?
  prompt               String?
  problemid            Int              @id @default(autoincrement())
  sectionid            Int?
  examtypeid           Int?
  title                String           @db.VarChar(200)
  text                 String
  difficulty           Int?
  correctattemptscount Int?             @default(0)
  totalattemptscount   Int?             @default(0)
  metadata             Json?
  creationdate         DateTime?        @default(now()) @db.Timestamp(6)
  addedDate            DateTime?        @default(now()) @db.Timestamp(6)
  isChildren           Boolean          @default(false)
  problemsSetId        Int?
  mocksectionid        Int?
  isMockQuestion       Boolean          @default(false)
  solution             Json?
  mockquestionnumber   Int?
  problemoptions       problemoptions[]
  examtypes            examtypes?       @relation(fields: [examtypeid], references: [examtypeid], onDelete: NoAction, onUpdate: NoAction)
  mocksections         mocksections?    @relation(fields: [mocksectionid], references: [mocksectionid], onDelete: NoAction, onUpdate: NoAction)
  ProblemsSet          ProblemsSet?     @relation(fields: [problemsSetId], references: [problemsSetId], onDelete: NoAction, onUpdate: NoAction)
  sections             sections?        @relation(fields: [sectionid], references: [sectionid], onDelete: NoAction, onUpdate: NoAction)
  problemtags          problemtags[]
  userattempts         userattempts[]
}

model problemtags {
  problemid Int
  tagid     Int
  problems  problems @relation(fields: [problemid], references: [problemid], onDelete: NoAction, onUpdate: NoAction)
  tags      tags     @relation(fields: [tagid], references: [tagid], onDelete: NoAction, onUpdate: NoAction)

  @@id([problemid, tagid])
}

model sections {
  sectionid             Int                     @id @default(autoincrement())
  examtypeid            Int?
  name                  String                  @db.VarChar(100)
  description           String?
  ProblemsSet           ProblemsSet[]
  mocksections          mocksections[]
  mocktestsectionscores mocktestsectionscores[]
  performance           performance[]
  problems              problems[]
  examtypes             examtypes?              @relation(fields: [examtypeid], references: [examtypeid], onDelete: NoAction, onUpdate: NoAction)
  tags                  tags[]
}

model tags {
  tagid           Int               @id @default(autoincrement())
  name            String            @db.VarChar(50)
  examtypeid      Int
  sectionid       Int
  problemssettags problemssettags[]
  problemtags     problemtags[]
  examtypes       examtypes         @relation(fields: [examtypeid], references: [examtypeid], onDelete: NoAction, onUpdate: NoAction)
  sections        sections          @relation(fields: [sectionid], references: [sectionid], onDelete: NoAction, onUpdate: NoAction)
}

model userattempts {
  attemptid               Int                           @id @default(autoincrement())
  userid                  Int?
  problemid               Int?
  timetaken               String?
  attemptdate             DateTime?                     @default(now()) @db.Timestamp(6)
  partialcorrectnessscore Decimal?                      @db.Decimal(5, 2)
  iscorrect               Boolean?
  selectedoptions         userattempt_selectedoptions[]
  problems                problems?                     @relation(fields: [problemid], references: [problemid], onDelete: NoAction, onUpdate: NoAction)
  users                   users?                        @relation(fields: [userid], references: [userid], onDelete: NoAction, onUpdate: NoAction)
}

model userattempt_selectedoptions {
  id            Int            @id @default(autoincrement())
  userattemptid Int
  optionid      Int
  problemoption problemoptions @relation(fields: [optionid], references: [optionid], onDelete: Cascade)
  userattempt   userattempts   @relation(fields: [userattemptid], references: [attemptid], onDelete: Cascade)

  @@unique([userattemptid, optionid])
}

model usermocktestattempts {
  attemptid             Int                     @id @default(autoincrement())
  userid                Int?
  mocktestid            Int?
  starttime             DateTime                @db.Timestamp(6)
  endtime               DateTime?               @db.Timestamp(6)
  totalscore            Decimal?                @db.Decimal(5, 2)
  isofficialattempt     Boolean?                @default(true)
  mocktestsectionscores mocktestsectionscores[]
  mocktests             mocktests?              @relation(fields: [mocktestid], references: [mocktestid], onDelete: NoAction, onUpdate: NoAction)
  users                 users?                  @relation(fields: [userid], references: [userid], onDelete: NoAction, onUpdate: NoAction)
}

model users {
  userid               Int                    @id @default(autoincrement())
  username             String                 @unique @db.VarChar(50)
  password             String                 @db.VarChar(100)
  email                String                 @unique @db.VarChar(100)
  registrationdate     DateTime?              @default(now()) @db.Timestamp(6)
  mocktestrankings     mocktestrankings[]
  performance          performance[]
  userattempts         userattempts[]
  usermocktestattempts usermocktestattempts[]
  userstreaks          userstreaks[]
}

model userstreaks {
  streakid      Int       @id @default(autoincrement())
  userid        Int?
  currentstreak Int?      @default(0)
  lasttestdate  DateTime? @db.Date
  higheststreak Int?      @default(0)
  users         users?    @relation(fields: [userid], references: [userid], onDelete: NoAction, onUpdate: NoAction)
}

model ProblemsSet {
  problemsSetId      Int               @id @default(autoincrement())
  sectionid          Int
  examtypeid         Int
  title              String
  type               String
  content            Json
  mocksectionid      Int?
  mockquestionnumber Int?
  examtypes          examtypes         @relation(fields: [examtypeid], references: [examtypeid])
  mocksections       mocksections?     @relation(fields: [mocksectionid], references: [mocksectionid])
  sections           sections          @relation(fields: [sectionid], references: [sectionid])
  problems           problems[]
  problemssettags    problemssettags[]
}

model problemssettags {
  problemsSetId Int
  tagid         Int
  ProblemsSet   ProblemsSet @relation(fields: [problemsSetId], references: [problemsSetId], onDelete: Cascade)
  tags          tags        @relation(fields: [tagid], references: [tagid], onDelete: Cascade)

  @@id([tagid, problemsSetId])
}

model mocksections {
  mocksectionid Int           @id @default(autoincrement())
  mocktestid    Int
  sectionnumber Int
  sectionid     Int
  ProblemsSet   ProblemsSet[]
  mocktests     mocktests     @relation(fields: [mocktestid], references: [mocktestid], onDelete: NoAction, onUpdate: NoAction)
  sections      sections      @relation(fields: [sectionid], references: [sectionid], onDelete: NoAction, onUpdate: NoAction)
  problems      problems[]
}
