-- Migration: Add performance-optimized database indexes
-- Purpose: Improve query performance for frequently accessed data patterns
-- Date: 17 June 2025
-- Chunk 7.4: Database index optimization for MIGRATION_ROADMAP.md

-- Problems table indexes (most frequently queried)
-- Index for main problem filtering by exam type and section
CREATE INDEX IF NOT EXISTS idx_problems_exam_section ON problems(examtypeid, sectionid) WHERE examtypeid IS NOT NULL AND sectionid IS NOT NULL;

-- Index for problem difficulty queries
CREATE INDEX IF NOT EXISTS idx_problems_difficulty ON problems(difficulty) WHERE difficulty IS NOT NULL;

-- Index for problem set children queries
CREATE INDEX IF NOT EXISTS idx_problems_problemssetid ON problems(problemsSetId) WHERE problemsSetId IS NOT NULL;

-- Index for creation date filtering (recent problems)
CREATE INDEX IF NOT EXISTS idx_problems_creation_date ON problems(creationdate) WHERE creationdate IS NOT NULL;

-- Index for problem type filtering
CREATE INDEX IF NOT EXISTS idx_problems_type ON problems(type) WHERE type IS NOT NULL;

-- Composite index for complex problem queries (exam + section + difficulty)
CREATE INDEX IF NOT EXISTS idx_problems_exam_section_difficulty ON problems(examtypeid, sectionid, difficulty) WHERE examtypeid IS NOT NULL AND sectionid IS NOT NULL;

-- User attempts table indexes (performance tracking)
-- Critical index for user-specific attempt queries
CREATE INDEX IF NOT EXISTS idx_userattempts_user_problem ON userattempts(userid, problemid) WHERE userid IS NOT NULL AND problemid IS NOT NULL;

-- Index for user attempts by date (recent activity)
CREATE INDEX IF NOT EXISTS idx_userattempts_user_date ON userattempts(userid, attemptdate) WHERE userid IS NOT NULL AND attemptdate IS NOT NULL;

-- Index for correctness analysis
CREATE INDEX IF NOT EXISTS idx_userattempts_correctness ON userattempts(iscorrect, userid) WHERE iscorrect IS NOT NULL AND userid IS NOT NULL;

-- Index for problem attempt statistics
CREATE INDEX IF NOT EXISTS idx_userattempts_problem_correctness ON userattempts(problemid, iscorrect) WHERE problemid IS NOT NULL AND iscorrect IS NOT NULL;

-- Problem tags relationship indexes (filtering)
-- Index for tag-based problem filtering
CREATE INDEX IF NOT EXISTS idx_problemtags_tagid ON problemtags(tagid);

-- Index for problem's tags lookup
CREATE INDEX IF NOT EXISTS idx_problemtags_problemid ON problemtags(problemid);

-- Composite index for tag filtering with problem lookup
CREATE INDEX IF NOT EXISTS idx_problemtags_tag_problem ON problemtags(tagid, problemid);

-- Tags table indexes (exam organization)
-- Index for tag queries by exam and section
CREATE INDEX IF NOT EXISTS idx_tags_exam_section ON tags(examtypeid, sectionid);

-- Index for tag name searches
CREATE INDEX IF NOT EXISTS idx_tags_name ON tags(name);

-- ProblemsSet table indexes (multi-part questions)
-- Index for problem set filtering by exam and section
CREATE INDEX IF NOT EXISTS idx_problemsset_exam_section ON ProblemsSet(examtypeid, sectionid);

-- Index for problem set type filtering
CREATE INDEX IF NOT EXISTS idx_problemsset_type ON ProblemsSet(type);

-- Performance table indexes (analytics)
-- Index for user performance by section
CREATE INDEX IF NOT EXISTS idx_performance_user_section ON performance(userid, sectionid);

-- Problem options indexes (answer processing)
-- Index for correct options lookup
CREATE INDEX IF NOT EXISTS idx_problemoptions_correct ON problemoptions(problemid, iscorrect) WHERE iscorrect = true;

-- Index for option group filtering
CREATE INDEX IF NOT EXISTS idx_problemoptions_group ON problemoptions(problemid, "group") WHERE "group" IS NOT NULL;

-- User attempt selected options indexes
-- Index for attempt option analysis
CREATE INDEX IF NOT EXISTS idx_userattempt_selectedoptions_attempt ON userattempt_selectedoptions(userattemptid);

-- Sections table indexes (exam structure)
-- Index for section lookup by exam type
CREATE INDEX IF NOT EXISTS idx_sections_examtype ON sections(examtypeid) WHERE examtypeid IS NOT NULL;

-- Users table performance indexes
-- Index for username lookups (authentication)
CREATE INDEX IF NOT EXISTS idx_users_username_lower ON users(LOWER(username));

-- Index for email lookups (authentication)
CREATE INDEX IF NOT EXISTS idx_users_email_lower ON users(LOWER(email));

-- Mock test related indexes (if using mock functionality)
-- Index for active mock tests
CREATE INDEX IF NOT EXISTS idx_mocktests_active ON mocktests(isactive, examtypeid) WHERE isactive = true;

-- Index for mock test dates
CREATE INDEX IF NOT EXISTS idx_mocktests_date ON mocktests(date) WHERE date IS NOT NULL;

-- User mock test attempts indexes
-- Index for user mock attempts
CREATE INDEX IF NOT EXISTS idx_usermocktest_user_test ON usermocktestattempts(userid, mocktestid) WHERE userid IS NOT NULL AND mocktestid IS NOT NULL;

-- Partial indexes for better performance on null-heavy columns
-- Index for non-null problem metadata
CREATE INDEX IF NOT EXISTS idx_problems_metadata_exists ON problems(problemid) WHERE metadata IS NOT NULL;

-- Index for problems with solutions
CREATE INDEX IF NOT EXISTS idx_problems_solution_exists ON problems(problemid) WHERE solution IS NOT NULL;

-- Covering indexes for common query patterns
-- Cover index for problem list queries (avoids table lookup)
CREATE INDEX IF NOT EXISTS idx_problems_list_covering ON problems(examtypeid, sectionid, difficulty) 
INCLUDE (problemid, title, type, creationdate, addedDate) 
WHERE examtypeid IS NOT NULL AND sectionid IS NOT NULL;

-- Cover index for user attempt analysis
CREATE INDEX IF NOT EXISTS idx_userattempts_analysis_covering ON userattempts(userid, problemid) 
INCLUDE (iscorrect, attemptdate, timetaken, partialcorrectnessscore) 
WHERE userid IS NOT NULL AND problemid IS NOT NULL;

-- Text search indexes for title and content searches
-- GIN index for problem title text search
CREATE INDEX IF NOT EXISTS idx_problems_title_gin ON problems USING gin(to_tsvector('english', title));

-- GIN index for problem text search
CREATE INDEX IF NOT EXISTS idx_problems_text_gin ON problems USING gin(to_tsvector('english', text));

-- Statistics update for better query planning
-- Analyze tables to update statistics for query optimizer
ANALYZE problems;
ANALYZE userattempts;
ANALYZE problemtags;
ANALYZE tags;
ANALYZE ProblemsSet;
ANALYZE performance;
ANALYZE sections;
ANALYZE users;