# User Analytics Components

This directory contains React components specific to the user analytics feature.

## Available Components

* **performance-charts.tsx**: Visual charts showing performance metrics
* **accuracy-meter.tsx**: Visual indicator of answer accuracy
* **streak-counter.tsx**: Displays user's current streak
* **time-tracker.tsx**: Tracks and displays time spent on questions
* **results-summary.tsx**: Summary of session results

## Component Details

### performance-charts.tsx

Charts for visualizing performance metrics:

```tsx
interface PerformanceChartsProps {
  userId: string;
  timeframe?: string;
  examType?: string;
  sectionId?: string;
}

function PerformanceCharts({ userId, timeframe = '30d', examType, sectionId }: PerformanceChartsProps) {
  const { metrics, loading, error } = usePerformanceMetrics(userId, {
    timeframe,
    examType,
    sectionId
  });
  
  if (loading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;
  
  return (
    <div className="performance-charts">
      <div className="chart-container">
        <h3>Accuracy Over Time</h3>
        <LineChart 
          data={metrics.accuracyTrend}
          xKey="date"
          yKey="accuracy"
          yAxisLabel="Accuracy (%)"
        />
      </div>
      
      <div className="chart-container">
        <h3>Performance by Question Type</h3>
        <BarChart 
          data={metrics.accuracyByType}
          xKey="type"
          yKey="accuracy"
          yAxisLabel="Accuracy (%)"
        />
      </div>
      
      <div className="chart-container">
        <h3>Time Spent vs. Accuracy</h3>
        <ScatterChart 
          data={metrics.timeVsAccuracy}
          xKey="timeSpent"
          yKey="accuracy"
          xAxisLabel="Avg. Time (seconds)"
          yAxisLabel="Accuracy (%)"
        />
      </div>
    </div>
  );
}
```

### accuracy-meter.tsx

Visual indicator of answer accuracy:

```tsx
interface AccuracyMeterProps {
  value: number;
  size?: 'small' | 'medium' | 'large';
  showLabel?: boolean;
}

function AccuracyMeter({ value, size = 'medium', showLabel = true }: AccuracyMeterProps) {
  // Determine color based on accuracy value
  const getColor = (accuracy: number) => {
    if (accuracy >= 80) return '#4CAF50'; // Green
    if (accuracy >= 60) return '#FFC107'; // Yellow
    return '#F44336'; // Red
  };
  
  const color = getColor(value);
  const sizeClass = `accuracy-meter-${size}`;
  
  return (
    <div className={`accuracy-meter ${sizeClass}`}>
      <div className="meter-container">
        <svg viewBox="0 0 100 100">
          <circle
            cx="50"
            cy="50"
            r="45"
            fill="none"
            stroke="#e6e6e6"
            strokeWidth="10"
          />
          <circle
            cx="50"
            cy="50"
            r="45"
            fill="none"
            stroke={color}
            strokeWidth="10"
            strokeDasharray={`${value * 2.83} 283`}
            strokeLinecap="round"
            transform="rotate(-90 50 50)"
          />
        </svg>
        <div className="meter-value" style={{ color }}>
          {Math.round(value)}%
        </div>
      </div>
      {showLabel && <div className="meter-label">Accuracy</div>}
    </div>
  );
}
```

### streak-counter.tsx

Displays user's current streak:

```tsx
interface StreakCounter