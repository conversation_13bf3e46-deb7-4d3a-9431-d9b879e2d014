import React, { useState, useCallback, useEffect, useMemo } from "react";
import { QuestionFloatingWindowProps } from "@/features/question-solving/hooks/(definitions)/questionDefinition";
import { useAttemptStore } from "@/features/question-solving/hooks/(pagination)/problemDefinition";
import { useResultData } from "@/features/user-analytics/hooks/resultWindowDefinitions";
import QuestionDisplay, {
   QuestionProps,
} from "@/features/question-solving/components/QuestionWindow/QuestionDisplay/questionDisplay";
import { Bookmark } from "lucide-react";

const truncateText = (text: string, maxLength: number) => {
   return text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;
};

const ResultWindow: React.FC<QuestionFloatingWindowProps> = ({ onClose }) => {
   const [questionDisplayProps, setQuestionDisplayProps] =
      useState<QuestionProps | null>(null);
   const { resultData } = useResultData();
   useEffect(() => {
      console.log("resultData", resultData);
   }, [resultData]);
   const { Attempt, resetAttempt, isFlagged, bookmarksEnabled } =
      useAttemptStore();
   const handleClose = useCallback(() => {
      resetAttempt();
      onClose();
   }, [resetAttempt, onClose]);

   const handleBack = useCallback(() => {
      setQuestionDisplayProps(null);
   }, []);

   const handlePageChange = (id: number) => {
      if (resultData) {
         const newResult = resultData.find((result) => result.problemid === id);
         if (!newResult) {
            console.log("didn't find the question id!");
            return;
         } else {
            console.log("New Result:-", newResult);
         }

         // Get all question IDs from resultData
         const questionIds = resultData.map(
            (resultdata) => resultdata.problemid
         );

         const isLongOptions = newResult?.options.some(
            (option) => option.optiontext.length > 10
         );

         const transformedOptions = newResult?.options.reduce(
            (acc, option, index) => {
               acc[index.toString()] = {
                  optiontext: option.optiontext,
                  group: option.group,
               };
               return acc;
            },
            {} as { [key: string]: { optiontext: string; group: string } }
         );

         console.log(newResult);
         setQuestionDisplayProps({
            type: newResult.type,
            title: newResult.title,
            text: newResult.text,
            options: transformedOptions,
            selectedOption: newResult.selectedoption,
            correctOption: newResult.correctoption,
            isLongOptions: isLongOptions,
            questionId: newResult.problemid,
            questionIds: questionIds,
            size: 7,
            showPagination: true,
            showSolution: true,
            solution: newResult.solution,
            handlePageChange: handlePageChange,
            metadata: newResult.metadata,
         });
      } else {
         console.log(
            "resultData itself is not captured properly, it is being null"
         );
      }
   };
   const handleQuestionClick = useCallback(
      (
         questionId: number,
         type: string,
         title: string,
         text: string,
         diagram: string | null,
         options: { optiontext: string; group: string }[],
         selectedOption: string[],
         correctOption: string[],
         solution: string,
         metadata: any
      ) => {
         const isLongOptions = options.some(
            (option) => option.optiontext.length > 10
         );
         // Get all question IDs from resultData
         const questionIds =
            resultData?.map((resultdata) => resultdata.problemid) || [];

         const transformedOptions = options.reduce((acc, option, index) => {
            acc[index.toString()] = {
               optiontext: option.optiontext,
               group: option.group,
            };
            return acc;
         }, {} as { [key: string]: { optiontext: string; group: string } });

         setQuestionDisplayProps({
            type,
            title,
            text,
            options: transformedOptions,
            selectedOption,
            correctOption,
            isLongOptions,
            questionId,
            questionIds,
            size: 7,
            showPagination: true,
            showSolution: true,
            solution,
            metadata,
            handlePageChange: handlePageChange,
         });
      },
      [resultData, handlePageChange]
   );
   function compareList(list1: string[], list2: string[]) {
      return list1.every((item) => list2.includes(item));
   }
   const content = useMemo(() => {
      if (questionDisplayProps) {
         return <QuestionDisplay {...questionDisplayProps} />;
      } else if (Attempt && resultData) {
         return (
            <div className="p-4">
               {
                  <div className="p-4">
                     <h2 className="text-2xl font-bold mb-6 text-center text-foreground">
                        Result
                     </h2>
                     <div className="mb-4 text-lg text-foreground">
                        <p>
                           Total questions:{" "}
                           <span className="font-semibold">
                              {resultData.length}
                           </span>
                        </p>
                        <p>
                           Score:{" "}
                           <span className="font-semibold">
                              {
                                 resultData.filter((item) =>
                                    compareList(
                                       item.correctoption,
                                       item.selectedoption
                                    )
                                 ).length
                              }
                           </span>
                           /{Attempt.length}
                        </p>
                        <p>
                           Accuracy:{" "}
                           <span className="font-semibold">
                              {(
                                 (resultData.filter((item) =>
                                    compareList(
                                       item.correctoption,
                                       item.selectedoption
                                    )
                                 ).length /
                                    Attempt.length) *
                                 100
                              ).toFixed(2)}
                              %
                           </span>
                        </p>
                        <p>
                           Total time taken:{" "}
                           <span className="font-semibold">
                              {(() => {
                                 const totalSeconds = resultData.reduce(
                                    (total, item) => {
                                       // Parse time format like "45 sec" or "2 min 30 sec"
                                       const timeStr = item.timetaken;
                                       let seconds = 0;

                                       if (timeStr.includes("min")) {
                                          const parts = timeStr.split(" ");
                                          const minIndex = parts.findIndex(
                                             (p) => p === "min"
                                          );
                                          const secIndex = parts.findIndex(
                                             (p) => p === "sec"
                                          );

                                          if (minIndex > 0)
                                             seconds +=
                                                parseInt(parts[minIndex - 1]) *
                                                60;
                                          if (
                                             secIndex > 0 &&
                                             secIndex !== minIndex + 1
                                          ) {
                                             seconds += parseInt(
                                                parts[secIndex - 1]
                                             );
                                          }
                                       } else if (timeStr.includes("sec")) {
                                          const match =
                                             timeStr.match(/(\d+)\s*sec/);
                                          if (match)
                                             seconds = parseInt(match[1]);
                                       }

                                       return total + seconds;
                                    },
                                    0
                                 );

                                 return totalSeconds >= 60
                                    ? `${Math.floor(totalSeconds / 60)}m ${
                                         totalSeconds % 60
                                      }s`
                                    : `${totalSeconds}s`;
                              })()}
                           </span>
                        </p>
                     </div>
                     <div className="overflow-x-auto">
                        <div className="md:table w-full border-collapse">
                           <div className="hidden md:table-row bg-muted">
                              <div className="md:table-cell p-2 font-semibold text-foreground">
                                 Question
                              </div>
                              <div className="md:table-cell p-2 font-semibold text-foreground">
                                 Title
                              </div>
                              <div className="md:table-cell p-2 font-semibold text-foreground">
                                 Your Answer
                              </div>
                              <div className="md:table-cell p-2 font-semibold text-foreground">
                                 Correct Answer
                              </div>
                              <div className="md:table-cell p-2 font-semibold text-foreground">
                                 Time Taken
                              </div>
                              <div className="md:table-cell p-2 font-semibold text-foreground">
                                 Status
                              </div>
                           </div>
                           {resultData.map((result) => {
                              const isCorrect = compareList(
                                 result.correctoption,
                                 result.selectedoption
                              );
                              const isQuestionFlagged =
                                 bookmarksEnabled &&
                                 isFlagged(result.problemid);

                              return (
                                 <div
                                    key={result.problemid}
                                    className={`block md:table-row border-b border-border ${
                                       isQuestionFlagged
                                          ? isCorrect
                                             ? "text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20 border-l-4 border-l-yellow-400 dark:border-l-yellow-500"
                                             : "text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 border-l-4 border-l-yellow-400 dark:border-l-yellow-500"
                                          : isCorrect
                                          ? "text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20"
                                          : "text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20"
                                    }`}
                                    onClick={() =>
                                       handleQuestionClick(
                                          result.problemid,
                                          result.type,
                                          result.title,
                                          result.text,
                                          result.diagram,
                                          result.options,
                                          result.selectedoption,
                                          result.correctoption,
                                          result.solution,
                                          result.metadata
                                       )
                                    }
                                 >
                                    <div className="md:table-cell p-2">
                                       <div className="flex items-center gap-2">
                                          {result.problemid.toString()}
                                          {isQuestionFlagged && (
                                             <Bookmark
                                                size={14}
                                                className="text-yellow-500 dark:text-yellow-400 fill-current"
                                             />
                                          )}
                                       </div>
                                    </div>
                                    <div className="md:table-cell p-2">
                                       {result.title.toString()}
                                    </div>
                                    <div
                                       className="md:table-cell p-2"
                                       title={result.selectedoption.join(", ")}
                                    >
                                       {truncateText(
                                          result.selectedoption.join(", "),
                                          30
                                       )}
                                    </div>
                                    <div
                                       className="md:table-cell p-2"
                                       title={result.correctoption.join(", ")}
                                    >
                                       {truncateText(
                                          result.correctoption.join(", "),
                                          30
                                       )}
                                    </div>
                                    <div className="md:table-cell p-2 font-medium">
                                       {result.timetaken}
                                    </div>
                                    <div className="md:table-cell p-2 font-semibold">
                                       {compareList(
                                          result.correctoption,
                                          result.selectedoption
                                       ) ? (
                                          <span className="text-green-600 dark:text-green-400">
                                             ✓ Correct
                                          </span>
                                       ) : (
                                          <span className="text-red-600 dark:text-red-400">
                                             ✗ Wrong
                                          </span>
                                       )}
                                    </div>
                                 </div>
                              );
                           })}
                        </div>
                     </div>
                  </div>
               }
            </div>
         );
      } else {
         return <>Loading...</>;
      }
   }, [
      questionDisplayProps,
      Attempt,
      resultData,
      handleQuestionClick,
      isFlagged,
      bookmarksEnabled,
   ]);

   return (
      <div className="fixed inset-0 flex items-center justify-center bg-black/50 backdrop-blur-sm z-50">
         <div className="relative bg-background p-6 shadow-xl rounded-lg w-11/12 max-w-5xl h-5/6 select-none overflow-hidden border border-border">
            <div className="absolute top-0 left-0 right-0 bg-background p-4 border-b z-10 flex justify-between items-center">
               <h1 className="text-xl font-semibold text-foreground">
                  {questionDisplayProps ? "Question Details" : "Results"}
               </h1>
               {questionDisplayProps && (
                  <h2 className="text-lg text-muted-foreground truncate">
                     {questionDisplayProps.title}
                  </h2>
               )}
               <button
                  className={`px-4 py-2 rounded-lg flex items-center gap-2 transition-colors duration-200 ${
                     questionDisplayProps
                        ? "bg-muted hover:bg-muted/80 text-foreground"
                        : "hover:bg-muted text-muted-foreground"
                  }`}
                  onClick={questionDisplayProps ? handleBack : handleClose}
               >
                  {questionDisplayProps ? (
                     <>
                        <span className="text-lg">&lt;</span>
                        <span className="text-base font-medium">Back</span>
                     </>
                  ) : (
                     <span className="text-xl">×</span>
                  )}
               </button>
            </div>
            <div className="h-full pt-16 overflow-auto">{content}</div>
         </div>
      </div>
   );
};

export default ResultWindow;
