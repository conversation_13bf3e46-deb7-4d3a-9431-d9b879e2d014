"use client";
import React, { useState, useEffect } from "react";
import Tab from "./tab";
import Panel from "./panel";
import { useProblemForPaginationStore } from "@/features/question-solving/hooks/(pagination)/problemDefinition";

interface PanelParentProps {
   children: React.ReactNode[];
   titles: string[];
   defaultIndex?: number;
}

const PanelParent = ({
   children,
   titles,
   defaultIndex = 0,
}: PanelParentProps) => {
   const {
      sectionName,
      sectionIndex,
      setSectionName,
      setSectionIndex,
      setPage,
   } = useProblemForPaginationStore();

   const handleTabClick = (index: number, title: string) => {
      setPage(1);
      setSectionName(title);
      setSectionIndex(index);
   };

   return (
      <div className="w-full">
         <div className="mb-6">
            <div
               className={`flex w-full rounded-lg bg-muted p-1 gap-1 flex-nowrap overflow-x-auto`}
            >
               {titles.map((title, index) => {
                  return (
                     <Tab
                        key={index}
                        onClick={() => handleTabClick(index, title)}
                        on={title === sectionName}
                     >
                        {title}
                     </Tab>
                  );
               })}
            </div>
         </div>

         <Panel active={true}>
            {children[sectionIndex] || children[0]}
         </Panel>
      </div>
   );
};

export default PanelParent;
