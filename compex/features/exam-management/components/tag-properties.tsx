"use client";
import { useEffect, useMemo, useCallback } from "react";
import React from "react";
import { TagLinks } from "@/shared/components/ui/button";
import { useProblemForPaginationStore } from "@/features/question-solving/hooks/(pagination)/problemDefinition";
import { Tag } from "@/features/question-solving/hooks/(definitions)/tagDefinition";
import { useState } from "react";
import { Switch } from "@/shared/components/ui/switch";
import { useTheme } from "next-themes";

interface SectionPromps {
   tagData: Tag[];
   displaySolvedQuestions: boolean;
   setDisplaySolvedQuestions: React.Dispatch<React.SetStateAction<boolean>>;
}

const Section: React.FC<SectionPromps> = React.memo(function Section({
   tagData,
   displaySolvedQuestions,
   setDisplaySolvedQuestions,
}) {
   const [tags, setTags] = useState<Tag[]>(tagData);
   const { selectedTags, setSelectedTags } = useProblemForPaginationStore();
   const { resolvedTheme } = useTheme();

   // Memoize theme-aware gradient colors to prevent recalculation on every render
   const getTagColors = useMemo(() => {
      const colorSets =
         resolvedTheme === "dark"
            ? [
                 { color1: "#4361ee", color2: "#3a0ca3" }, // Blue to purple
                 { color1: "#4cc9f0", color2: "#4895ef" }, // Light blue to blue
                 { color1: "#f72585", color2: "#7209b7" }, // Pink to purple
                 { color1: "#4d908e", color2: "#277da1" }, // Teal to blue
                 { color1: "#f8961e", color2: "#f3722c" }, // Orange to darker orange
              ]
            : [
                 { color1: "#ff7e5f", color2: "#feb47b" }, // Coral to peach
                 { color1: "#7f53ac", color2: "#647dee" }, // Purple to blue
                 { color1: "#56ab2f", color2: "#a8e063" }, // Green to light green
                 { color1: "#614385", color2: "#516395" }, // Purple to blue
                 { color1: "#eecda3", color2: "#ef629f" }, // Beige to pink
              ];

      return (index: number) => colorSets[index % colorSets.length];
   }, [resolvedTheme]);

   useEffect(() => {
      setTags(tagData);
   }, [tagData]);
   // Debounce tag selection to prevent excessive API calls
   useEffect(() => {
      const timeoutId = setTimeout(() => {
         setSelectedTags([
            ...tags.filter((tag) => tag.isActive).map((tag) => tag.name),
         ]);
      }, 300); // 300ms debounce

      return () => clearTimeout(timeoutId);
   }, [tags]); // ❌ Remove setSelectedTags from dependencies - Zustand functions are stable
   const activate = useCallback(async (index: number) => {
      setTags((prevTags) => {
         const newTagStatus = [...prevTags];
         newTagStatus[index] = {
            ...newTagStatus[index],
            isActive: !prevTags[index].isActive,
         };
         return newTagStatus;
      });
   }, []); // Remove tags dependency to prevent infinite loop
   return (
      <>
         <div className="w-full flex flex-col justify-between">
            <div className="flex flex-wrap gap-3">
               {tags.map((tag, index) => {
                  const { color1, color2 } = getTagColors(index);
                  return (
                     <div onClick={() => activate(index)} key={index}>
                        <TagLinks
                           name={tag.name}
                           count={tag.count}
                           isActive={tag.isActive}
                           href="#"
                           color1={color1}
                           color2={color2}
                           key={index}
                        />
                     </div>
                  );
               })}
            </div>

            <div className="flex justify-end mt-5 gap-4 items-center">
               <span className="text-foreground">
                  Display solved questions:
               </span>{" "}
               <Switch
                  checked={displaySolvedQuestions}
                  onCheckedChange={() => {
                     setDisplaySolvedQuestions(!displaySolvedQuestions);
                  }}
               />
            </div>
         </div>
      </>
   );
});

export default Section;
