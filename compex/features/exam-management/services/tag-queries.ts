import { prisma } from "@/shared/lib/configs/prisma";
import { Tag } from "@/features/question-solving/hooks/(definitions)/tagDefinition";

export async function getTags(examtypeid: number, sectionid: number) {
   const tag: Tag[] = await Promise.all(
      (
         await prisma.tags.findMany({
            where: {
               examtypeid: examtypeid,
               sectionid: sectionid,
            },
            select: {
               tagid: true,
               name: true,
            },
         })
      ).map(async (value) => {
         const count: number = await prisma.problemtags.count({
            where: {
               tagid: value.tagid,
            },
         });
         return {
            name: value.name,
            examtypeid: examtypeid,
            sectionid: sectionid,
            count: count,
            isActive: false,
         };
      })
   );
   return tag;
}