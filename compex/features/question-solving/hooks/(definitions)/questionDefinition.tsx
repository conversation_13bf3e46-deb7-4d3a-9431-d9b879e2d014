export interface Option {
   optionid: number;
   optiontext: string;
}

export interface Metadata {
   diagram: string | undefined;
   hints: string[];
   tags: string[];
}

export interface QuestionData {
   problemid: number;
   text: string;
   title: string;
   problemoptions: Option;
   metadata: Metadata;
}

export interface QuestionFloatingWindowProps {
   onClose: () => void;
   showTimer?: boolean;
}

export interface PaginationProps {
   questionIds: (number | { [key: string]: number[] })[];
   currentQuestionId: number;
   size: number;
   onPageChange: (id: number) => void;
}
