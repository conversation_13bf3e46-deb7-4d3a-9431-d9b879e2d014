/**
 * @deprecated This file is being migrated to focused stores.
 * Use the new focused stores from @/shared/stores/problems instead.
 * 
 * Compatibility layer for existing code during migration.
 */

// Import and re-export focused stores and types
import {
  useProblemForPaginationStore,
  useNavigationStore,
  useQuestionCacheStore,
  useAttemptsStore,
  type Problem,
  type ProblemsSet,
  type HybridProblem,
  type ProblemsResponse,
  type QuestionState,
  type AttemptsStore,
} from "@/shared/stores/problems";

// Re-export for compatibility
export {
  useProblemForPaginationStore,
  type Problem,
  type ProblemsSet,
  type HybridProblem,
  type ProblemsResponse,
};

// Re-export types
export { type QuestionState } from "@/shared/stores/problems";
export type UserAttempts = AttemptsStore;

/**
 * Compatibility wrapper for questionList that combines navigation and cache stores
 * @deprecated Use focused stores directly from @/shared/stores/problems
 */
export function questionList() {
  const navigation = useNavigationStore();
  const cache = useQuestionCacheStore();
  
  const storeAPI = {
    // Navigation state and actions
    questionIds: navigation.questionIds,
    questionDataList: navigation.questionDataList,
    windowSize: navigation.windowSize,
    start: navigation.start,
    setStart: navigation.setStart,
    setQuestionIds: navigation.setQuestionIds,
    pasteQuestionDataList: navigation.pasteQuestionDataList,
    setQuestionDataList: navigation.setQuestionDataList,
    
    // Cache state and actions
    questionCache: cache.questionCache,
    cacheSize: cache.cacheSize,
    prefetchSize: cache.prefetchSize,
    lastPrefetchIndex: cache.lastPrefetchIndex,
    addToCache: cache.addToCache,
    getFromCache: cache.getFromCache,
    markQuestionAccessed: cache.markQuestionAccessed,
    evictLRUFromCache: cache.evictLRUFromCache,
    clearCache: cache.clearCache,
    
    // Prefetch with navigation context
    prefetchQuestions: (currentIndex: number) => 
      cache.prefetchQuestions(currentIndex, navigation.questionIds),
      
    // Zustand compatibility - getState returns the same object
    getState: () => storeAPI,
  };
  
  return storeAPI;
}

/**
 * Legacy compatibility export
 * @deprecated Use useAttemptsStore from focused stores
 */
export const useAttemptStore = useAttemptsStore;

// Legacy export for backward compatibility
export const questionDetails = {
  type: "",
  problemid: 0,
  title: "questionTitle", 
  text: "question",
  problemoptions: [{ optionid: 0, optiontext: "" }],
  metadata: {},
};

// Keep legacy interface for backward compatibility
export interface PorblemsResponse {
  problemData: HybridProblem[];
  totalProblems: number;
}
