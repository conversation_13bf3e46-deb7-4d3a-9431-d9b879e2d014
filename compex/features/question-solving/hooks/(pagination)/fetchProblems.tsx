"use client";
import { useQuery } from "@tanstack/react-query";
import { useState, useEffect, useMemo } from "react";
import {
   useQuestionCacheStore,
   useNavigationStore,
} from "@/shared/stores/problems";
import {
   Problem,
   ProblemsSet,
   ProblemsResponse,
   QuestionState,
   useProblemForPaginationStore,
   questionList,
   UserAttempts,
   HybridProblem,
   useAttemptStore,
} from "./problemDefinition";

function sortProblems(
   filteredProblemData: HybridProblem[],
   categoryIndex: number,
   sortOrder: 1 | 0 | -1
): HybridProblem[] {
   if (sortOrder === 0 || filteredProblemData.length === 0)
      return filteredProblemData; // No sorting

   const categoryKey = Object.keys(filteredProblemData[0])[
      categoryIndex
   ] as keyof HybridProblem;

   return filteredProblemData.sort((a, b) => {
      const aValue = a[categoryKey];
      const bValue = b[categoryKey];

      // Handle undefined values
      if (aValue === undefined && bValue === undefined) return 0;
      if (aValue === undefined) return sortOrder === 1 ? 1 : -1;
      if (bValue === undefined) return sortOrder === 1 ? -1 : 1;

      if (sortOrder === 1) {
         // Ascending
         return aValue < bValue ? -1 : 1;
      } else {
         // Descending
         return aValue > bValue ? -1 : 1;
      }
   });
}
function isProblemSet(item: HybridProblem) {
   if ("isExpanded" in item) {
      return true;
   } else {
      return false;
   }
}
const fetchProblems_and_ProblemsSet = async (
   userid: number,
   examName: string,
   sectionName: string,
   Tags: string[]
): Promise<ProblemsResponse> => {
   // console.log("Fetching problems and problem sets with:", {
   //    examName,
   //    sectionName,
   //    Tags,
   // });
   // console.log("Tags:", Tags);
   const tags = Tags.length > 0 ? Tags : null;

   try {
      // Initiate both fetch requests in parallel
      const [response_A, response_B] = await Promise.all([
         fetch("/api/problems/getProblems", {
            method: "POST",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify({
               userid,
               examName,
               sectionName,
               tags: tags, // Ensure this is explicitly passed as null when no tags
            }),
         }),
         fetch("/api/problems/getProblemsSets", {
            method: "POST",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify({
               userid, // Pass userid to getProblemsSets
               examName,
               sectionName,
               tags: tags, // Ensure this is explicitly passed as null when no tags
            }),
         }),
      ]);

      if (!response_A.ok || !response_B.ok) {
         throw new Error(
            `Failed to fetch data. Problems Status: ${response_A.status}, ProblemSets Status: ${response_B.status}`
         );
      }
      const data_A = await response_A.json();
      const data_B = await response_B.json();
      let parentProblems: HybridProblem[] = [];
      if (data_B.problemSetWithProblems) {
         parentProblems = data_B.problemSetWithProblems.map(
            (set: ProblemsSet) => ({
               problemsSetId: set.problemsSetId,
               title: set.title,
               difficulty:
                  Math.round(
                     set.problems.reduce(
                        (acc, problem) => acc + problem.difficulty,
                        0
                     ) / set.problems.length
                  ) || 0,
               addedDate: set.problems[0].addedDate,
               isParent: true,
               problems: set.problems.map((problem) => ({
                  ...problem,
                  iscorrect: problem.iscorrect, // Explicitly map iscorrect
               })),
               isExpanded: false,
            })
         );
         // continue from here!
      } else {
         parentProblems = [];
      }
      // Assign absolute indices to all problems and problem sets
      const combinedData = [...data_A.problemData, ...parentProblems];
      let absoluteIndex = 0;

      const hybridDataWithIndices = combinedData.map((item) => {
         if (isProblemSet(item)) {
            const problemSet = item as ProblemsSet;
            const updatedProblems = problemSet.problems.map((problem) => ({
               ...problem,
               absoluteIndex: absoluteIndex++,
            }));
            return {
               ...problemSet,
               problems: updatedProblems,
               absoluteIndex: absoluteIndex - updatedProblems.length, // Set to first child's index
            };
         } else {
            return {
               ...item,
               absoluteIndex: absoluteIndex++,
            };
         }
      });

      const hybridData = {
         problemData: hybridDataWithIndices,
         totalProblems: data_A.totalProblems + parentProblems.length,
      };
      return hybridData;
   } catch (error: any) {
      console.error("Error in fetchProblems_and_ProblemsSet:", error);
      throw error;
   }
};

export const usePaginatedProblems = () => {
   const [filteredProblemData, setFilteredProblemData] =
      useState<HybridProblem[]>();
   const {
      page,
      pageSize,
      examName,
      sectionName,
      selectedTags,
      searchedText,
      categoryIndex,
      sortOrder,
      problems_or_problemsset,
      isShuffled,
   } = useProblemForPaginationStore();
   const { userId } = useAttemptStore();
   const { questionIds, setQuestionIds } = questionList();
   const [previousData, setPreviousData] = useState<ProblemsResponse | null>(
      null
   );

   // Memoize selectedTags to prevent query key from changing on every render
   const memoizedSelectedTags = useMemo(() => {
      return [...selectedTags].sort(); // Sort to ensure consistent array comparison
   }, [selectedTags]);

   const { status, data, error } = useQuery({
      queryKey: [
         "problems",
         userId,
         examName,
         sectionName,
         memoizedSelectedTags,
         // Removed page and pageSize from queryKey to prevent refetch on pagination
      ],
      queryFn: () =>
         fetchProblems_and_ProblemsSet(
            userId,
            examName,
            sectionName,
            selectedTags
         ),
      staleTime: 5 * 60 * 1000, // 5 minutes - much longer to prevent excessive refetches
      refetchOnWindowFocus: false,
   });
   useEffect(() => {
      if (status === "success" && data) {
         setPreviousData(data);

         // Use shuffled data if available, otherwise use fresh data
         const sourceData =
            isShuffled && problems_or_problemsset.length > 0
               ? problems_or_problemsset
               : data.problemData;

         setFilteredProblemData(
            sourceData.filter((problem: HybridProblem) =>
               problem.title.toLowerCase().includes(searchedText.toLowerCase())
            )
         );
      }
   }, [data, status, isShuffled, problems_or_problemsset, searchedText]);

   // Memoize question IDs calculation to prevent unnecessary recalculations
   const questionIdsArray = useMemo(() => {
      if (!filteredProblemData) return [];

      return filteredProblemData.map((problem) => {
         if (isProblemSet(problem)) {
            let temp = [];
            for (let i = 0; i < (problem as ProblemsSet).problems.length; i++) {
               temp.push(
                  parseInt((problem as ProblemsSet).problems[i].problemid)
               );
            }
            let res: { [key: string]: number[] } = {};
            res[(problem as ProblemsSet).problemsSetId] = temp;
            return res;
         }
         return parseInt(Object.values(problem)[0], 10);
      });
   }, [filteredProblemData]);

   useEffect(() => {
      if (questionIdsArray.length > 0) {
         setQuestionIds(questionIdsArray);
      }
   }, [questionIdsArray, setQuestionIds]);

   //Sorting problems
   if (filteredProblemData?.length) {
      sortProblems(filteredProblemData, categoryIndex, sortOrder);
   }

   const a = (page - 1) * pageSize;
   const b = pageSize;
   const finalData = data
      ? {
           problemData: filteredProblemData?.slice(a, a + b) || [],
           totalProblems: filteredProblemData?.length || data.totalProblems,
        }
      : { problemData: [], totalProblems: 0 };

   return {
      status,
      error,
      data: finalData,
      previousData,
      fullData: filteredProblemData, // Add full dataset for shuffling
   };
};

const fetchQuestionDetails = async (
   questionIds: (number | { [key: string]: number[] })[],
   windowSize: number,
   start: number,
   getFromCache: (id: number) => QuestionState | null
): Promise<QuestionState[]> => {
   let getQuestionOfIds: number[] = [];
   const cachedQuestions: QuestionState[] = [];

   // region getQuestionOfIds ✅
   let questionsCovered = 0;
   for (
      let i = 0;
      getQuestionOfIds.length < windowSize && i < questionIds.length;
      i++
   ) {
      let id: number;
      if (typeof questionIds[i] === "number") {
         id = questionIds[i] as number;
         if (questionsCovered >= start) {
            // First try to get from cache
            const cached = getFromCache(id);
            if (cached) {
               cachedQuestions.push(cached);
            } else {
               getQuestionOfIds.push(id);
            }
         }
         questionsCovered++;
      } else {
         for (
            let j = 0;
            getQuestionOfIds.length + cachedQuestions.length < windowSize &&
            j < Object.values(questionIds[i])[0].length;
            j++
         ) {
            id = Object.values(questionIds[i])[0][j];
            if (questionsCovered >= start) {
               // First try to get from cache
               const cached = getFromCache(id);
               if (cached) {
                  cachedQuestions.push(cached);
               } else {
                  getQuestionOfIds.push(id);
               }
            }
            questionsCovered++;
         }
      }
   }
   // endregion

   let fetchedQuestions: QuestionState[] = [];
   if (getQuestionOfIds.length > 0) {
      const response = await fetch("/api/problems/getQuestions", {
         method: "POST",
         headers: {
            "Content-Type": "application/json",
         },
         body: JSON.stringify({
            questionId: getQuestionOfIds,
         }),
      });
      fetchedQuestions = await response.json();
   }

   // Combine cached and fetched questions
   const allQuestions = [...cachedQuestions, ...fetchedQuestions];
   return allQuestions;
};

export const callQuestions = () => {
   const { page, pageSize, categoryIndex, sortOrder } =
      useProblemForPaginationStore();
   const {
      questionIds,
      start,
      questionDataList,
      windowSize,
      pasteQuestionDataList,
      getFromCache,
      prefetchQuestions,
   } = questionList();

   const { status, data, error } = useQuery({
      queryKey: ["questions", questionIds, start, windowSize],
      queryFn: () => {
         //start value comes here
         return fetchQuestionDetails(
            questionIds,
            windowSize,
            start,
            getFromCache
         );
      },
      staleTime: 10 * 60 * 1000, // 10 minutes - questions rarely change
      refetchOnWindowFocus: false,
      enabled: start >= 0,
   });

   useEffect(() => {
      if (status === "success" && data) {
         const sortedData: QuestionState[] = questionIds
            .map((id) =>
               data.find((question) => {
                  if (typeof id === "number") {
                     return question.problemid === id;
                  } else {
                     return Object.values(id)[0].includes(question.problemid);
                  }
               })
            )
            .filter((question) => question !== undefined) as QuestionState[];
         pasteQuestionDataList(sortedData); // if the question data list is empty, then put all the questions in the QuestionDataList,
      }
   }, [status, data, questionIds, pasteQuestionDataList]);

   // Separate effect for prefetching to avoid dependency issues
   useEffect(() => {
      if (status === "success" && start >= 0 && questionIds.length > 0) {
         // Use setTimeout to defer prefetching and avoid blocking main thread
         const timeoutId = setTimeout(() => {
            // Use the focused stores directly for prefetching
            const cache = useQuestionCacheStore.getState();
            const navigation = useNavigationStore.getState();
            cache
               .prefetchQuestions(start, navigation.questionIds)
               .catch((error) => console.warn("Prefetch failed:", error));
         }, 500); // Increase delay to allow main UI to settle

         return () => clearTimeout(timeoutId);
      }
   }, [start, status]);

   return {
      status,
      data: questionDataList,
      error,
   };
};

export const submitUserAttempt = async (data: UserAttempts) => {
   const response = await fetch("/api/problems/returnAttempt", {
      method: "POST",
      headers: {
         "Content-Type": "application/json",
      },
      body: JSON.stringify({
         ...data,
      }),
   });
   return response.json();
};
