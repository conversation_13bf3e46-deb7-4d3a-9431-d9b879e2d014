import { group } from "console";
import React, { useEffect, useState } from "react";

interface TC23TemplateProps {
   isAttempting: boolean;
   options: { [key: string]: { optiontext: string; group: string } };
   selectedOptions: string[] | null;
   correctOptions?: string[];
   handleOptionClick?: (option: string[]) => void;
   isLongOptions: boolean;
}

const TC23Template: React.FC<TC23TemplateProps> = ({
   isAttempting,
   options,
   selectedOptions,
   correctOptions,
   handleOptionClick,
   isLongOptions,
}) => {
   const groups = Array.from(
      new Set(Object.values(options).map((opt) => opt.group))
   );
   const groupCount =
      groups.length / (Object.keys(options).length / groups.length);

   const [selectedSubOptions, setSelectedSubOptions] = useState<
      {
         optiontext: string;
         group: string;
         groupIndex: number;
      }[]
   >();
   const groupedOptions = Object.entries(options).reduce(
      (
         acc: { optiontext: string; group: string }[][] | undefined,
         [key, value]
      ) => {
         if (!acc) {
            acc = [[{ optiontext: value.optiontext, group: value.group }]];
            return acc;
         }
         const lastGroup = acc[acc.length - 1];
         if (lastGroup.length < groups.length) {
            lastGroup.push({
               optiontext: value.optiontext,
               group: value.group,
            });
         } else {
            acc.push([{ optiontext: value.optiontext, group: value.group }]);
         }
         return acc;
      },
      undefined
   );

   useEffect(() => {
      // console.log("selectedOptions are:-", selectedOptions);
      if (
         (!selectedOptions || selectedOptions.length === 0) &&
         selectedSubOptions &&
         selectedSubOptions.length > 0
      ) {
         setSelectedSubOptions([]);
      } // this code is to replicate the clear command.
      else {
         if (selectedOptions && groupedOptions && selectedSubOptions) {
            // need to check if the selectedOptions are already having optiontexts which are present in selectedOptions
            let count = 0;
            for (let i = 0; i < selectedSubOptions.length; i++) {
               if (selectedOptions.includes(selectedSubOptions[i].optiontext)) {
                  count++;
               }
            }
            if (count !== selectedOptions.length) {
               let addSelectedOptions: {
                  optiontext: string;
                  group: string;
                  groupIndex: number;
               }[] = [];
               for (let i = 0; i < selectedOptions.length; i++) {
                  for (let j = 0; j < groupedOptions.length; j++) {
                     for (let k = 0; k < groupedOptions[j].length; k++) {
                        if (
                           selectedOptions[i] ===
                           groupedOptions[j][k].optiontext
                        ) {
                           addSelectedOptions.push({
                              optiontext: selectedOptions[i],
                              group: groupedOptions[j][k].group,
                              groupIndex: j,
                           });
                           break;
                        }
                     }
                  }
               }
               setSelectedSubOptions(addSelectedOptions);
            }
         }
      }
   }, [selectedOptions]);

   const handleSubOptionClicking = (
      optiontext: string,
      group: string,
      groupIndex: number
   ) => {
      let tempSelectedSubOptions: {
         optiontext: string;
         group: string;
         groupIndex: number;
      }[] = [];
      if (!selectedSubOptions) {
         let firstSelectedOption: {
            optiontext: string;
            group: string;
            groupIndex: number;
         } = {
            optiontext,
            group,
            groupIndex,
         };
         tempSelectedSubOptions.push(firstSelectedOption);
         setSelectedSubOptions([firstSelectedOption]);
      } else {
         tempSelectedSubOptions = [...selectedSubOptions];
         let isOptionAdded = false;
         for (let i = 0; i < tempSelectedSubOptions.length; i++) {
            if (tempSelectedSubOptions[i].groupIndex === groupIndex) {
               tempSelectedSubOptions[i].group = group;
               tempSelectedSubOptions[i].optiontext = optiontext;
               isOptionAdded = true;
               break;
            }
         }
         if (!isOptionAdded) {
            tempSelectedSubOptions.push({
               optiontext,
               group,
               groupIndex,
            });
            isOptionAdded = true;
         }
         setSelectedSubOptions(tempSelectedSubOptions);
      }
      if (tempSelectedSubOptions) {
         const selectedOptions = tempSelectedSubOptions.map(
            (opt) => opt.optiontext
         );
         handleOptionClick && handleOptionClick(selectedOptions);
      }
   };

   function checkIfSelected(optiontext: string) {
      if (selectedOptions && Array.isArray(selectedOptions)) {
         return selectedOptions.includes(optiontext);
      } else {
         return false;
      }
   }

   function isCorrectOption(optiontext: string) {
      return correctOptions?.includes(optiontext);
   }

   // console.log("groupedOptions are:-", groupedOptions); // groupedOptions are [[optiontext, group]]
   // console.log("groups are:-", groups);  // A, B, C, D
   return (
      <div className="flex gap-6">
         {groupedOptions &&
            Object.entries(groupedOptions).map((groupKey, groupIndex) => (
               <div key={groupIndex} className="flex-1">
                  <div className="font-bold mb-2">Blank {groupIndex + 1}</div>
                  <div className="flex flex-col gap-3">
                     {groupedOptions[groupIndex].map(
                        ({ optiontext, group }, index) => (
                           <div
                              key={index}
                              className={`p-3 border rounded-lg cursor-pointer 
                  ${
                     isAttempting
                        ? checkIfSelected(optiontext)
                           ? "bg-primary/10 border-primary text-foreground"
                           : "bg-background border-border text-foreground"
                        : checkIfSelected(optiontext)
                        ? isCorrectOption(optiontext)
                           ? "bg-green-100 dark:bg-green-900/30 border-green-500 text-foreground"
                           : "bg-red-100 dark:bg-red-900/30 border-red-500 text-foreground"
                        : isCorrectOption(optiontext)
                        ? "bg-green-100 dark:bg-green-900/30 border-green-500 text-foreground"
                        : "bg-background border-border text-foreground"
                  }
                `}
                              onClick={() =>
                                 handleOptionClick &&
                                 handleSubOptionClicking(
                                    optiontext,
                                    group,
                                    groupIndex
                                 )
                              }
                           >
                              {optiontext}
                           </div>
                        )
                     )}
                  </div>
               </div>
            ))}
      </div>
   );
};

export default TC23Template;
