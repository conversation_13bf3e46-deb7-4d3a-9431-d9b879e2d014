import React, { useEffect, useState } from "react";

interface NETemplateProps {
   isAttempting: boolean;
   optionSelected: string;
   handleSetAnswer: ((option: string | string[]) => void) | undefined;
   correctAnswer: string[] | undefined;
   handleClear?: () => void;
}

const NETemplate: React.FC<NETemplateProps> = ({
   isAttempting,
   optionSelected,
   handleSetAnswer,
   correctAnswer,
   handleClear,
}) => {
   // Initialize with optionSelected if it exists, otherwise empty string
   const [answer, setAnswer] = useState<string>(optionSelected || "");
   
   // Update local state when optionSelected changes (when navigating back to question)
   useEffect(() => {
      if (optionSelected && optionSelected !== answer) {
         setAnswer(optionSelected);
      }
   }, [optionSelected]);
   
   useEffect(() => {
      if (answer === "" && handleClear) {
         handleClear();
         return;
      }
      if (handleSetAnswer && answer !== "") {
         handleSetAnswer(answer);
      }
   }, [answer, handleClear, handleSetAnswer]);
   return (
      <div>
         <div className="flex flex-col gap-4">
            <input
               type="number"
               className={`w-full border-2 border-gray-300 rounded-md p-2 ${
                  correctAnswer !== undefined
                     ? parseFloat(correctAnswer[0]) ===
                       parseFloat(optionSelected)
                        ? "bg-green-100 border-green-300 text-green-700"
                        : "bg-red-100 border-red-300 text-red-700"
                     : ""
               }`}
               placeholder="Enter your answer"
               value={correctAnswer ? optionSelected : answer}
               onChange={(e) => setAnswer(e.target.value)}
               disabled={correctAnswer !== undefined}
            />
            {!isAttempting && correctAnswer !== undefined && (
               <label className="correct-answer text-green-600">
                  Correct Answer:&nbsp;
                  {correctAnswer[0]}
               </label>
            )}
         </div>
      </div>
   );
};

export default NETemplate;
