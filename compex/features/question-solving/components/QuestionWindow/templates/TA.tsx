import React, { useEffect } from "react";
import TS_TAQuestion from "./TS-TA";

interface TAQuestionProps {
   text: string;
   metadata: any;
}

export const TAQuestion: React.FC<TAQuestionProps> = ({ text, metadata }) => {
   const table = metadata.tables[0];
   const type = table.type;
   if (type === "time-series table") {
      return (
         <>
            <p>{text}</p>
            <TS_TAQuestion text={text} metadata={metadata} />
         </>
      );
   }
   console.log(table);
   let columns: string[] = [];
   if ("columns" in table) {
      columns = table.columns.map((col: any) =>
         typeof col === "string" ? col : Object.keys(col)[0]
      );
   } else if ("data_columns" in table) {
      columns = table.data_columns.map((col: any) =>
         typeof col === "string" ? col : Object.keys(col)[0]
      );
   } else {
      columns = [];
   }

   const getTableType = () => {
      if (columns.some((col: string) => col === "Performance Rating")) {
         return "performance";
      }
      if (columns.some((col: string) => col.includes("Q"))) {
         return "quarterly";
      }
      return "standard";
   };

   const renderTableHeaders = () => {
      return (
         <thead>
            <tr className="bg-gray-100">
               {columns.map((column: string, index: number) => (
                  <th
                     key={index}
                     className="border border-gray-300 px-4 py-2 text-left"
                  >
                     {column}
                  </th>
               ))}
            </tr>
         </thead>
      );
   };

   const renderTableCell = (value: any, columnName: string) => {
      if (typeof value === "number") {
         const isPercentage = [
            "percentage",
            "share",
            "rate",
            "ratio",
            "growth",
            "market",
            "efficiency",
         ].some((term) => columnName.toLowerCase().includes(term));

         if (isPercentage) {
            return `${value}%`;
         }
         return value.toLocaleString();
      }

      if (columnName === "Performance Rating") {
         return (
            <span
               className={
                  value === "Not Acceptable"
                     ? "text-red-500"
                     : value === "Acceptable"
                     ? "text-green-500"
                     : "text-yellow-500"
               }
            >
               {value}
            </span>
         );
      }

      return value;
   };

   const renderTableBody = () => {
      return (
         <tbody>
            {table.data.map((row: any[], rowIndex: number) => (
               <tr
                  key={rowIndex}
                  className={rowIndex % 2 === 0 ? "bg-white" : "bg-gray-50"}
               >
                  {row.map((value: any, colIndex: number) => (
                     <td
                        key={colIndex}
                        className={`border border-gray-300 px-4 py-2 ${
                           typeof value === "number"
                              ? "text-right"
                              : "text-left"
                        }`}
                     >
                        {renderTableCell(value, columns[colIndex])}
                     </td>
                  ))}
               </tr>
            ))}
         </tbody>
      );
   };

   return (
      <div className="space-y-4">
         <div>{text}</div>
         <div className="overflow-x-auto">
            <table className="min-w-full border-collapse border border-gray-300">
               {renderTableHeaders()}
               {renderTableBody()}
            </table>
         </div>
      </div>
   );
};

export default TAQuestion;
