import React, { useEffect } from "react";
import { reDirectGraph } from "./ParentChild";
interface GITemplateProps {
   metadata: any;
}

const GITemplate: React.FC<GITemplateProps> = ({ metadata }) => {
   return (
      <div>
         {metadata.map((item: any, index: number) => {
            let mtData = { graph: item };
            return <div key={index}>{reDirectGraph(item.graph, mtData)}</div>;
         })}
      </div>
   );
};

interface GIQuestionProps {
   text: string;
   options: { [key: string]: { optiontext: string } };
   selectedOption: string[];
   correctOption?: string[];
   handleOptionClick?: (option: string | string[]) => void;
   isLongOptions: boolean;
   handleClear?: () => void;
   metadata: any;
}

export const GIQuestion: React.FC<GIQuestionProps> = ({
   text,
   options,
   selectedOption,
   correctOption,
   handleOptionClick,
   isLongOptions,
   metadata,
}) => {
   // console.log(metadata);
   // Split text into parts using the blank pattern (whitespace + underscores + whitespace)
   const blankPattern = /_{3,}/g;
   const parts = text.split(blankPattern);
   // Convert options object to array and split into first 3 and next 3
   const optionEntries = Object.entries(options);
   const firstBlankOptions = optionEntries.slice(0, 3);
   const secondBlankOptions = optionEntries.slice(3, 6);

   const handleSelectChange = (index: number, value: string) => {
      const newSelected = [...selectedOption];
      newSelected[index] = value;
      handleOptionClick?.(newSelected);
   };

   const showValidation = correctOption && correctOption.length > 0;
   const isCorrect = showValidation
      ? selectedOption.every((opt, index) => opt === correctOption?.[index])
      : true;

   return (
      <>
         <div>
            {metadata && (
               <div className="mb-6">
                  {metadata.graphs.map((item: any, index: number) => {
                     let mtData = { graph: item };
                     return (
                        <div key={index}>
                           {reDirectGraph(item.graph, mtData)}
                        </div>
                     );
                  })}
               </div>
            )}
         </div>
         <div
            style={{
               lineHeight: "2",
               fontSize: "16px",
               fontWeight: "bold",
            }}
         >
            {showValidation ? (
               <div>
                  {/* User's Answer */}
                  <div style={{ marginBottom: "16px", color: "#ef4444" }}>
                     {parts.map((part, index) => (
                        <React.Fragment key={`user-${index}`}>
                           <span>{part}</span>
                           {index < parts.length - 1 && (
                              <span
                                 style={{
                                    textDecoration: "underline",
                                    margin: "0 8px",
                                 }}
                              >
                                 {selectedOption[index] || "______"}
                              </span>
                           )}
                        </React.Fragment>
                     ))}
                  </div>

                  {/* Correct Answer */}
                  <div style={{ color: "#22c55e" }}>
                     {parts.map((part, index) => (
                        <React.Fragment key={`correct-${index}`}>
                           <span>{part}</span>
                           {index < parts.length - 1 && (
                              <span
                                 style={{
                                    textDecoration: "underline",
                                    margin: "0 8px",
                                 }}
                              >
                                 {correctOption?.[index] || "______"}
                              </span>
                           )}
                        </React.Fragment>
                     ))}
                  </div>
               </div>
            ) : (
               parts.map((part, index) => (
                  <React.Fragment key={index}>
                     <span style={{ marginRight: "8px" }}>{part}</span>
                     {index < parts.length - 1 && (
                        <select
                           value={selectedOption[index] || ""}
                           onChange={(e) =>
                              handleSelectChange(index, e.target.value)
                           }
                           style={{
                              margin: "0 12px",
                              padding: "6px 12px",
                              borderRadius: "4px",
                              border: "1px solid #ccc",
                              backgroundColor: "white",
                              cursor: "pointer",
                              minWidth: "120px",
                              boxShadow: "0 1px 2px rgba(0,0,0,0.1)",
                           }}
                        >
                           <option value="" disabled style={{ color: "#999" }}>
                              Select option
                           </option>
                           {(index === 0
                              ? firstBlankOptions
                              : secondBlankOptions
                           ).map(([key, opt]) => (
                              <option
                                 key={key}
                                 value={opt.optiontext}
                                 style={{ padding: "4px 8px" }}
                              >
                                 {opt.optiontext}
                              </option>
                           ))}
                        </select>
                     )}
                  </React.Fragment>
               ))
            )}
         </div>
      </>
   );
};

export default GITemplate;
