import React from "react";

interface metadataTypeA {
   columns: string[];
   data: Record<string, Record<string, number>>;
   description: string;
   index: string[];
   type: string;
}
interface metadataTypeB {
   type: string;
   graph: {
      data: Record<string, Record<string, number>>;
      index: string[];
      columns: string[];
   };
   description: string;
}
interface PivotTableProps {
   metadata: metadataTypeA | metadataTypeB;
}

const PivotTable: React.FC<PivotTableProps> = ({ metadata }) => {
   // Helper function to format numbers
   const formatValue = (value: number) => {
      if (typeof value === "number") {
         return value.toString();
      }
      return value;
   };

   // Extract the properly structured data from metadata
   let columns, data, index, description;
   if ("graph" in metadata) {
      columns = metadata.graph.columns;
      data = metadata.graph.data;
      index = metadata.graph.index;
      description = metadata.description;
   } else {
      columns = metadata.columns;
      data = metadata.data;
      index = metadata.index;
      description = metadata.description;
   }
   // Dynamic index column name - use indexName if provided, or empty string

   return (
      <div className="flex flex-col gap-4 w-full">
         <p className="text-sm text-gray-600">{description}</p>
         <div className="overflow-x-auto">
            <table className="min-w-full bg-white border border-gray-300">
               {/* Header */}
               <thead>
                  <tr className="bg-gray-100">
                     <th className="border border-gray-300 px-4 py-2"> </th>
                     {columns.map((column: string) => (
                        <th
                           key={column}
                           className="border border-gray-300 px-4 py-2"
                        >
                           {column}
                        </th>
                     ))}
                  </tr>
               </thead>
               {/* Body */}
               <tbody>
                  {index?.map((rowKey: string, rowIdx: number) => (
                     <tr
                        key={rowKey}
                        className={rowIdx % 2 === 0 ? "bg-white" : "bg-gray-50"}
                     >
                        <td className="border border-gray-300 px-4 py-2 font-medium">
                           {rowKey}
                        </td>
                        {columns.map((column: string) => {
                           const value = data[rowKey]?.[column];
                           return (
                              <td
                                 key={`${rowKey}-${column}`}
                                 className="border border-gray-300 px-4 py-2 text-right"
                              >
                                 {value !== undefined ? formatValue(value) : ""}
                              </td>
                           );
                        })}
                     </tr>
                  ))}
               </tbody>
            </table>
         </div>
      </div>
   );
};

export default PivotTable;
