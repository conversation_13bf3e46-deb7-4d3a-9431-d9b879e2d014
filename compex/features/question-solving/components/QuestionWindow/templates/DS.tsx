// filepath: /Users/<USER>/Desktop/compex/compex/app/ui/problems/questionfloatingwindow/Templates/DS.tsx
import React, { useEffect } from "react";

interface DSTemplateProps {
   text: string;
   metadata: {
      passage: string;
      statements: string[];
   };
   questionDisplayfn: (text: string) => React.JSX.Element[];
}

const DSTemplate: React.FunctionComponent<DSTemplateProps> = ({
   text,
   metadata,
   questionDisplayfn,
}) => {
   if (metadata.passage && metadata.statements) {
      return (
         <>
            <p>
               <b>Passage: </b>
               {questionDisplayfn(metadata.passage)}
            </p>
            <br />
            <p>
               <b>Statement (A): </b>
               {questionDisplayfn(metadata.statements[0])}
            </p>
            <p>
               <b>Statement (B): </b>
               {questionDisplayfn(metadata.statements[1])}
            </p>
            <br />
            <b>Question: </b>
            {questionDisplayfn(text)}
         </>
      );
   } else {
      return <></>;
   }
};

export default DSTemplate;
