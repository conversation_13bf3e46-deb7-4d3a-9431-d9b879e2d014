import React from "react";

interface RCTemplateProps {
   metadata: {
      problemsSetId: number;
      content: {
         passages: string[];
      };
      title: string;
   };
}

const RCTemplate: React.FunctionComponent<RCTemplateProps> = ({ metadata }) => {
   if (metadata.content.passages) {
      return (
         <>
            <b>{metadata.title}</b>
            <br />
            <div className="mt-3 flex flex-col gap-2">
               {metadata.content.passages.map((passage, index) => {
                  return (
                     <div
                        key={index}
                        dangerouslySetInnerHTML={{
                           __html: passage,
                        }}
                     />
                  );
               })}
            </div>
         </>
      );
   } else {
      return <></>;
   }
};

export default RCTemplate;
