import React, { useState, useEffect } from "react";

interface SETemplateProps {
   isAttempting: boolean;
   options: { [key: string]: { optiontext: string } };
   selectedOptions: string[] | null;
   correctOptions?: string[];
   handleOptionClick?: (option: string[]) => void;
   isLongOptions: boolean;
}

const SETemplate: React.FC<SETemplateProps> = ({
   isAttempting,
   options,
   selectedOptions,
   correctOptions,
   handleOptionClick,
   isLongOptions,
}) => {
   const [selectedSubOptions, setSelectedSubOptions] = useState<string[]>([]);
   const [locked, setLocked] = useState(false);
   const totalOptions = Object.values(options).length;
   const handleSubOptionClick = (optiontext: string) => {
      if (selectedSubOptions.includes(optiontext)) {
         const updatedOptions = selectedSubOptions.filter(
            (opt) => opt !== optiontext
         );
         console.log(updatedOptions);
         setSelectedSubOptions(updatedOptions);
      } else if (selectedSubOptions.length < 2) {
         setSelectedSubOptions([...selectedSubOptions, optiontext]);
      }
      // if already selected 2 options, do nothing
   };
   useEffect(() => {
      if (selectedSubOptions.length >= 0) {
         handleOptionClick?.(selectedSubOptions);
      }
      if (selectedSubOptions.length === 2) {
         setLocked(true);
      } else {
         setLocked(false);
      }
   }, [selectedSubOptions, handleOptionClick]);
   useEffect(() => {
      // Reset selections if selectedOptions becomes null or non-array
      if (!Array.isArray(selectedOptions)) {
         setSelectedSubOptions([]);
      }
   }, [selectedOptions]);

   const checkIfSelected = (optiontext: string) => {
      if (selectedOptions && Array.isArray(selectedOptions)) {
         return selectedOptions.includes(optiontext);
      }
      return false;
   };

   const isCorrectOption = (optiontext: string) => {
      return correctOptions?.includes(optiontext);
   };
   return (
      <div className="flex flex-col gap-3">
         {Object.entries(options).map(([key, { optiontext }]) => (
            <div
               key={key}
               className={`p-3 border rounded-lg cursor-pointer
                  ${isLongOptions ? "text-sm" : "text-base"}
                  ${
                     isAttempting
                        ? checkIfSelected(optiontext)
                           ? "bg-primary/10 border-primary text-foreground"
                           : locked
                           ? "bg-background border-muted text-foreground"
                           : "bg-background border-border text-foreground"
                        : checkIfSelected(optiontext)
                        ? isCorrectOption(optiontext)
                           ? "bg-green-100 dark:bg-green-900/30 border-green-500 text-foreground"
                           : "bg-red-100 dark:bg-red-900/30 border-red-500 text-foreground"
                        : isCorrectOption(optiontext)
                        ? "bg-green-100 dark:bg-green-900/30 border-green-500 text-foreground"
                        : "bg-background border-border text-foreground"
                  }
                  `}
               onClick={() => checkIfSelected(optiontext)}
            >
               <label className={`flex items-center gap-2`}>
                  <input
                     type="checkbox"
                     checked={checkIfSelected(optiontext)}
                     onChange={() => handleSubOptionClick(optiontext)}
                  />
                  {optiontext}
               </label>
            </div>
         ))}
      </div>
   );
};

export default SETemplate;
