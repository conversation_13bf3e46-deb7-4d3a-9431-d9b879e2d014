import React, { useState } from "react";
import { reDirectGraph } from "./ParentChild";
import SourceTable from "./tables/SourceTable";
interface MSRTemplateProps {
   metadata: any;
}

const MSRTemplate: React.FC<MSRTemplateProps> = ({ metadata }) => {
   const [activeTabIdx, setActiveTabIdx] = useState(0);
   const sources = metadata.content?.sources;
   // Ensure sources is an array and has items
   if (!Array.isArray(sources) || sources.length === 0) {
      return (
         <div className="p-4 text-gray-700">
            No sources available for this MSR question!
         </div>
      );
   }
   const activeSource = sources[activeTabIdx];
   return (
      <div className="flex flex-col gap-4">
         <h2 className="text-lg font-bold">{metadata.title}</h2>
         <div className="flex border-b border-gray-300">
            {sources.map((source: any, idx: number) => (
               <button
                  key={idx}
                  onClick={() => setActiveTabIdx(idx)}
                  className={`px-4 py-2 text-sm font-medium ${
                     activeTabIdx === idx
                        ? "text-blue-600 border-b-2 border-blue-600"
                        : "text-gray-600 hover:text-blue-600"
                  }`}
                  aria-current={activeTabIdx === idx ? "page" : undefined}
               >
                  Source {idx + 1}
               </button>
            ))}
         </div>
         {activeSource.source_info && (
            <p className="text-sm text-gray-600 mb-2">
               {activeSource.source_info}
            </p>
         )}
         {/* Handling Passages */}
         {activeSource.content?.type.trim().toLowerCase() === "passage" &&
            activeSource.content.passages && (
               <div className="bg-gray-50 p-3 rounded">
                  {activeSource.content.passages.map(
                     (passage: string, idx: number) => {
                        return <p key={`${idx}`}>{passage}</p>;
                     }
                  )}
               </div>
            )}
         {/* Handling Tables */}
         {activeSource.content?.type?.toLowerCase().includes("table") && (
            <div className="mt-2">
               <SourceTable
                  tableData={activeSource.content}
                  description={activeSource.content.description}
               />
               {reDirectGraph("pivot table", activeSource.content)}
            </div>
         )}
         {/* Handling Graph Type */}
         {activeSource.content?.graph && (
            <div className="mt-2">
               {reDirectGraph(
                  activeSource.content.type || activeSource.content.graph,
                  {
                     description: activeSource.content.description,
                     graph: activeSource.content,
                  }
               )}
            </div>
         )}
      </div>
   );
};

export default MSRTemplate;
