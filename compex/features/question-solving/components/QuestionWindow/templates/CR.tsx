import React, { useEffect } from "react";

interface CRTemplateProps {
   text: string;
   metadata?: any;
}

const CRTemplate: React.FC<CRTemplateProps> = ({ text, metadata }) => {
   useEffect(() => {
      console.log(metadata);
   }, [metadata]);
   return (
      <div>
         {metadata.passages.map((passage: string, index: number) => {
            return <p key={index}>{passage}</p>;
         })}
         <br />
         <br />
         <span dangerouslySetInnerHTML={{ __html: text }} />{" "}
      </div>
   );
};

export default CRTemplate;
