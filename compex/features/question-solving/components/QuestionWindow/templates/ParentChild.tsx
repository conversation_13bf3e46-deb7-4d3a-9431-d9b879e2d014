import React, { useEffect } from "react";
//calling main templates
import RCTemplate from "./RC";
import MSRTemplate from "./msr";
//calling graphs
import BarGraphTemplate from "./graphs/BarGraph";
import Bar<PERSON>hartTemplate from "./graphs/BarChart";
import GroupedBarChartTemplate from "./graphs/GroupedBarChart";
import StackedBarChartTemplate from "./graphs/StackedBarChart";
import ScatterPlotTemplate from "./graphs/ScatterPlot";
import LineGraphTemplate from "./graphs/LineGraph";
import PieChartTemplate from "./graphs/PieChart";
import LineChartTemplate from "./graphs/LineChart";

//calling tables
import SourceTable from "./tables/SourceTable";
import PivotTable from "./tables/PivotTable";
interface ParentChildTemplateProps {
   type: string;
   text: string;
   metadata: any;
}

const ParentChildTemplate: React.FC<ParentChildTemplateProps> = ({
   type,
   text,
   metadata,
}) => {
   console.log(`metadata of type ${type} is`, metadata);
   if (type === "RC") {
      return <RCTemplate metadata={metadata} />;
   }
   // Checking if the questions is of Type MSR
   if (metadata.content?.sources) {
      return <MSRTemplate metadata={metadata} />;
   }
   // normal graphical question content:-
   return (
      <div className="flex flex-col gap-2">
         <h2 className="text-lg font-bold">{metadata.title}</h2>
         {metadata.content?.map((item: any, index: number) => {
            return <div key={index}>{reDirectGraph(item?.type, item)}</div>;
         })}
      </div>
   );
};

export const reDirectGraph = (type: string, metadata: any) => {
   console.log(`metadata of type ${type} is`, metadata);

   // Check if this is a grouped bar chart (has years as keys)
   const isGroupedBarChart =
      type === "bar chart" &&
      metadata?.graph?.data?.length > 0 &&
      Object.keys(metadata.graph.data[0]).some((key) => !isNaN(parseInt(key)));

   if (type === "bar graph") {
      return <BarGraphTemplate metadata={metadata} />;
   } else if (type === "bar chart" && isGroupedBarChart) {
      return <GroupedBarChartTemplate metadata={metadata} />;
   } else if (type === "bar chart") {
      return <BarChartTemplate metadata={metadata} />;
   } else if (type === "stacked bar chart") {
      return <StackedBarChartTemplate metadata={metadata} />;
   } else if (type === "scatter plot" || metadata?.graph === "scatter plot") {
      return <ScatterPlotTemplate metadata={metadata} />;
   } else if (type === "line graph") {
      return <LineGraphTemplate metadata={metadata} />;
   } else if (type === "line chart") {
      return <LineChartTemplate metadata={metadata} />;
   } else if (type === "pie chart" || metadata?.graph === "pie chart") {
      return <PieChartTemplate metadata={metadata} />;
   } else if (type === "pivot table" || metadata?.table === "pivot table") {
      console.log("creating pivot table");
      return <PivotTable metadata={metadata} />;
   } else if (type === "Passage") {
      return metadata.passage.map((item: string, index: number) => {
         return <p key={index}>{item}</p>;
      });
   } else {
      return <div>Graph of type "{type}" not prepared yet</div>;
   }
};
export default ParentChildTemplate;
