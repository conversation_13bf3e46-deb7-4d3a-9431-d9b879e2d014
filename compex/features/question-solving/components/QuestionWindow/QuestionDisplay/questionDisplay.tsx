import React, { useEffect, useState, useMemo } from "react";
import AnimatedPagination from "../../Pagination/animatedPagination";
import { classNames } from "@/shared/lib/utils/formatting";
import TC23Template from "../templates/TC-23";
import SETemplate from "../templates/SE";
import NETemplate from "../templates/NE";
import ParentChildTemplate from "../templates/ParentChild";
import katex from "katex";
import "katex/dist/katex.min.css";
import { getTemplateComponent } from "../templates/templateMap";
const op = ["A", "B", "C", "D", "E", "F", "G", "H"];

export interface QuestionProps {
   type: string;
   title: string;
   text: string;
   options: { [key: string]: { optiontext: string } };
   selectedOption: string[];
   correctOption?: string[];
   handleOptionClick?: (option: string | string[]) => void;
   isLongOptions: boolean;
   handleClear?: () => void;
   handleClearAll?: () => void;
   handlePageChange?: (id: number) => void;
   questionIds?: (number | { [key: string]: number[] })[];
   questionId?: number;
   size?: number;
   showPagination?: boolean;
   showSolution?: boolean;
   solution?: any;
   metadata?: any;
}

const QuestionDisplay: React.FC<QuestionProps> = React.memo(({
   type,
   title,
   text,
   options,
   selectedOption,
   correctOption,
   handleOptionClick,
   isLongOptions,
   handleClear,
   handleClearAll,
   handlePageChange,
   questionIds,
   questionId,
   size,
   showPagination = false,
   showSolution = false,
   solution,
   metadata,
}) => {
   const [isAttempting, setIsAttempting] = useState(true);
   useEffect(() => {
      if (solution) {
         setIsAttempting(false);
      }
   }, [solution]);

   useEffect(() => {
      console.log(metadata);
      if (metadata?.length > 0) {
         console.log("2:", metadata);
      }
   }, [metadata]);

   const Template = getTemplateComponent(type);

   // Memoize static layout components
   const staticLayout = useMemo(() => {
      return {
         hasParentChild: metadata && "problemsSetId" in metadata,
         widthClass: type === "RC" ||
                    type === "CR" ||
                    (metadata && "problemsSetId" in metadata)
                       ? "w-1/4"
                       : type === "GI"
                       ? "w-4/5"
                       : isLongOptions
                       ? "w-2/3"
                       : "w-1/2"
      };
   }, [type, metadata, isLongOptions]);

   // Memoize the question content that changes
   const questionContent = useMemo(() => {
      if (metadata) {
         if (type === "RC" && "problemsSetId" in metadata) {
            return (
               <div>
                  <h3 className="font-medium mb-2">
                     <b>Question:</b>
                  </h3>
                  {parseSolutionText(text)}
               </div>
            );
         } else {
            return (
               <Template
                  text={text}
                  metadata={metadata}
                  options={options}
                  selectedOption={selectedOption}
                  correctOption={correctOption}
                  handleOptionClick={handleOptionClick}
                  isLongOptions={isLongOptions}
                  questionDisplayfn={parseSolutionText}
                  handleClear={handleClear}
               />
            );
         }
      } else if (type === "DS") {
         return (
            <>
               <b>Passage</b>
               <div>
                  {parseSolutionText(metadata?.passage || "")}
               </div>
               <b>Statements:</b>
               {metadata?.statements?.map(
                  (item: string, index: number) => {
                     return (
                        <p key={index}>
                           <b>{index + 1}. </b>
                           {parseSolutionText(item)}
                        </p>
                     );
                  }
               )}
               <b>Question:</b>
               <div>{parseSolutionText(text)}</div>
            </>
         );
      } else {
         return (
            <>
               <b>Question:</b>
               {parseSolutionText(text)}
            </>
         );
      }
   }, [type, text, metadata, options, selectedOption, correctOption, handleOptionClick, isLongOptions, handleClear, Template]);

   return (
      <div className="flex flex-col h-full">
         {/* Main content area - single scrollable container */}
         <div className="flex-grow overflow-y-auto">
            <div className="flex flex-row space-x-4 h-full">
               {staticLayout.hasParentChild && (
                  <div className="flex-grow w-1/3 bg-muted p-4 overflow-y-auto">
                     <ParentChildTemplate
                        type={type}
                        text={text}
                        metadata={metadata}
                     />
                  </div>
               )}
               <div
                  className={`flex-grow ${staticLayout.widthClass} flex flex-col overflow-y-auto`}
               >
                  <div className="bg-muted p-4 mb-4 overflow-y-auto">
                     {questionContent}
                  </div>
                  {type !== "GI" ? (
                     <div>
                        <OptionsGrid
                           type={type}
                           options={options}
                           isLongOptions={isLongOptions}
                           isAttempting={isAttempting}
                           selectedOption={selectedOption}
                           correctOption={correctOption}
                           handleOptionClick={handleOptionClick}
                           handleClear={handleClear}
                           op={op}
                        />
                     </div>
                  ) : (
                     <></>
                  )}
               </div>
               {!staticLayout.hasParentChild && (
                  <div
                     className={`flex-grow ${
                        isLongOptions
                           ? "hidden"
                           : type === "RC"
                           ? "w-1/3"
                           : "w-1/2"
                     } bg-muted p-4 flex justify-center items-center`}
                  />
               )}
            </div>
            {showSolution && solution && (
               <div className="w-full mt-7 p-4 bg-muted/50 border border-border rounded-lg">
                  <h3 className="text-lg font-semibold text-foreground">
                     Solution
                  </h3>
                  {solution.solution ? (
                     <div className="space-y-2 text-foreground">
                        {parseSolutionText(solution.solution)}
                     </div>
                  ) : (
                     <div className="space-y-2 text-foreground">
                        {solution.map((sol: string, index: number) => (
                           <li key={index} style={{ listStyleType: "none" }}>
                              {parseSolutionText(sol)}
                           </li>
                        ))}
                     </div>
                  )}
               </div>
            )}
         </div>
         {/*Here starts the pagination component*/}
         <StaticPaginationSection
            showSolution={showSolution}
            showPagination={showPagination}
            questionIds={questionIds}
            questionId={questionId}
            handlePageChange={handlePageChange}
            size={size}
            handleClear={handleClear}
            handleClearAll={handleClearAll}
         />
      </div>
   );
});

interface OptionData {
   optiontext: string;
   group?: string;
}

interface OptionsGridProps {
   type: string;
   options: Record<string, OptionData>;
   isLongOptions: boolean;
   isAttempting: boolean;
   selectedOption: string | string[] | null;
   correctOption: string | string[] | undefined;
   handleOptionClick?: (optionText: string | string[]) => void;
   handleClear?: () => void;
   op: string[];
}

const OptionsGrid: React.FC<OptionsGridProps> = ({
   type,
   options,
   isLongOptions,
   isAttempting,
   selectedOption,
   correctOption,
   handleOptionClick,
   handleClear,
   op,
}) => {
   if (type === "NE") {
      return (
         <NETemplate
            isAttempting={isAttempting}
            optionSelected={selectedOption?.[0] || ""}
            handleSetAnswer={handleOptionClick}
            correctAnswer={correctOption as string[]}
            handleClear={handleClear}
         />
      );
   } else if (type.includes("TC")) {
      return (
         <TC23Template
            isAttempting={isAttempting}
            options={
               options as Record<string, { optiontext: string; group: string }>
            }
            selectedOptions={(selectedOption as string[]) || null}
            correctOptions={correctOption as string[]}
            handleOptionClick={handleOptionClick}
            isLongOptions={isLongOptions}
         />
      );
   } else if (type === "SE") {
      return (
         <SETemplate
            isAttempting={isAttempting}
            options={options}
            selectedOptions={(selectedOption as string[]) || null}
            correctOptions={correctOption as string[]}
            handleOptionClick={handleOptionClick}
            isLongOptions={isLongOptions}
         />
      );
   }
   return (
      <div
         className={`grid ${
            isLongOptions ? "grid-cols-1" : "grid-cols-2"
         } gap-4`}
      >
         {Object.entries(options).map(([key, { optiontext }]) => (
            <div
               key={key}
               className={`p-2 border rounded-lg cursor-pointer ${
                  isAttempting
                     ? selectedOption?.includes(optiontext)
                        ? "bg-primary/10 border-primary text-foreground"
                        : "bg-background border-border text-foreground"
                     : selectedOption?.includes(optiontext)
                     ? correctOption?.includes(optiontext)
                        ? "bg-green-100 dark:bg-green-900/30 border-green-500 text-foreground"
                        : "bg-red-100 dark:bg-red-900/30 border-red-500 text-foreground"
                     : correctOption?.includes(optiontext)
                     ? "bg-green-100 dark:bg-green-900/30 border-green-500 text-foreground"
                     : "bg-background border-border text-foreground"
               }`}
               onClick={() =>
                  handleOptionClick && handleOptionClick(optiontext)
               }
            >
               <b>{op[parseInt(key, 10)]} :</b> {optiontext.toString()}
            </div>
         ))}
      </div>
   );
};

const parseSolutionText = (text: string) => {
   if (!text) return [];

   // Handle both literal \n and actual newlines
   const normalizedText = text.replace(/\\n/g, '\n');
   
   // Split by actual newlines and also handle multiple newlines for paragraph breaks
   const paragraphs = normalizedText.split(/\n\s*\n/);
   const renderedElements: React.JSX.Element[] = [];

   paragraphs.forEach((paragraph, paragraphIndex) => {
      if (paragraph.trim()) {
         // Process each paragraph for math expressions
         const mathProcessed = processMathInText(paragraph.trim());
         
         renderedElements.push(
            <div 
               key={`paragraph-${paragraphIndex}`}
               className="mb-4 leading-relaxed"
               dangerouslySetInnerHTML={{ __html: mathProcessed }}
            />
         );
      }
   });

   return renderedElements;
};

const processMathInText = (text: string): string => {
   // First handle KaTeX expressions
   let processedText = text;
   
   // Handle inline math expressions ($...$)
   processedText = processedText.replace(/\$([^$]+)\$/g, (match, latex) => {
      try {
         return katex.renderToString(latex, { throwOnError: false });
      } catch (error) {
         console.error("KaTeX rendering error (inline):", error, "Input:", latex);
         return `<span style="color: red; font-family: monospace;">${match}</span>`;
      }
   });
   
   // Handle block math expressions (\\...\\)
   processedText = processedText.replace(/\\\\([^\\]+)\\\\/g, (match, latex) => {
      try {
         return katex.renderToString(latex, { throwOnError: false, displayMode: true });
      } catch (error) {
         console.error("KaTeX rendering error (block):", error, "Input:", latex);
         return `<span style="color: red; font-family: monospace;">${match}</span>`;
      }
   });
   
   // Handle strikethrough text (~~...~~) and make it more visible
   processedText = processedText.replace(/~~([^~]+)~~/g, '<span style="text-decoration: line-through; opacity: 0.7;">$1</span>');
   
   // Convert single newlines to line breaks
   processedText = processedText.replace(/\n/g, '<br />');
   
   return processedText;
};

// Memoized static pagination section to prevent unnecessary re-renders
const StaticPaginationSection: React.FC<{
   showSolution: boolean;
   showPagination: boolean;
   questionIds?: (number | { [key: string]: number[] })[];
   questionId?: number;
   handlePageChange?: (id: number) => void;
   size?: number;
   handleClear?: () => void;
   handleClearAll?: () => void;
}> = React.memo(({
   showSolution,
   showPagination,
   questionIds,
   questionId,
   handlePageChange,
   size,
   handleClear,
   handleClearAll,
}) => {
   return (
      <div className="mt-4 border-t pt-4 bg-background">
         <div
            className={classNames(
               "flex flex-col gap-4",
               !showSolution ? "items-center" : ""
            )}
         >
            <div className="w-full flex items-center justify-between gap-4">
               {showPagination &&
                  questionIds &&
                  questionId !== undefined &&
                  handlePageChange &&
                  size && (
                     <div className="flex-1">
                        <AnimatedPagination
                           questionIds={questionIds}
                           currentQuestionId={questionId}
                           onPageChange={handlePageChange}
                           windowSize={9}
                        />
                     </div>
                  )}
               {handleClear && (
                  <button
                     onClick={handleClear}
                     className="flex-shrink-0 px-4 py-2 bg-muted hover:bg-muted/80 text-foreground font-medium rounded-lg border border-border transition-colors duration-200 flex items-center gap-2"
                  >
                     Clear
                  </button>
               )}
               {handleClearAll && (
                  <button
                     onClick={handleClearAll}
                     className="flex-shrink-0 px-4 py-2 bg-muted hover:bg-muted/80 text-foreground font-medium rounded-lg border border-border transition-colors duration-200 flex items-center gap-2"
                  >
                     Clear All
                  </button>
               )}
            </div>
         </div>
      </div>
   );
});

export default QuestionDisplay;
