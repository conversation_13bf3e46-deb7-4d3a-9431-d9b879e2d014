import React, { useEffect, useState, useCallback, useRef } from "react";
import { QuestionFloatingWindowProps } from "@/features/question-solving/hooks/(definitions)/questionDefinition";
import {
   useAttemptStore,
   questionList,
} from "@/features/question-solving/hooks/(pagination)/problemDefinition";
import {
   callQuestions,
} from "@/features/question-solving/hooks/(pagination)/fetchProblems";
import { submitUserAttempt } from "@/features/question-solving/services/question-attempt-service";
import QuestionDisplay from "./QuestionDisplay/questionDisplay";
import {
   questionResult,
   useResultData,
} from "@/features/user-analytics/hooks/resultWindowDefinitions";
import Loading from "@/shared/components/feedback/loading";
import { useTimerStore } from "@/features/user-analytics/hooks/timerDefinition";
import Timer from "@/features/user-analytics/components/timer/timer";
import { Bookmark } from "lucide-react";

const QuestionAttempt: React.FC<QuestionFloatingWindowProps> = ({
   onClose,
   showTimer = true,
}) => {
   const { status, data: question } = callQuestions();
   const [questionData, setQuestionData] = useState(question[0]);
   useEffect(() => {
      // console.log("questions:-", question);
      setQuestionData(question[0]);
   }, [question]);
   // console.log(questionData.problemoptions);
   const { start, questionIds, setStart, getFromCache, markQuestionAccessed } =
      questionList();
   const {
      userId,
      Attempt,
      addAttempt,
      resetAttempt,
      toggleFlag,
      isFlagged,
      bookmarksEnabled,
      setBookmarksEnabled,
      clearFlags,
   } = useAttemptStore();
   const {
      switchQuestion,
      recordOptionSelectedTime,
      clearRecordedTime,
      getElapsedTime,
      currentQuestionId,
   } = useTimerStore();
   useEffect(() => {
      // console.log("Attempt: ", Attempt);
   }, [Attempt]);
   const [selectedOption, setSelectedOption] = useState<string[]>([]);
   const [isLongOptions, setIsLongOptions] = useState(false);
   const { setResultData } = useResultData();

   // Use ref to avoid dependency issues with getFromCache
   const getCacheRef = useRef(getFromCache);
   getCacheRef.current = getFromCache;

   // Optimize question loading with memoization and reduced re-renders
   useEffect(() => {
      const flattenedIds = flattenQuestionIds();
      const targetQuestionId = flattenedIds[start];

      if (targetQuestionId && targetQuestionId !== questionData?.problemid) {
         // Handle timer switching
         switchQuestion(currentQuestionId, targetQuestionId);

         // First try to get from cache for instant loading
         const cachedQuestion = getCacheRef.current(targetQuestionId);

         if (cachedQuestion) {
            // Use cached data immediately for faster UI response
            console.log(
               `✅ Serving question ${targetQuestionId} from cache (instant load)`
            );
            markQuestionAccessed(targetQuestionId); // Mark as accessed for LRU tracking
            setQuestionData(cachedQuestion);
            setIsLongOptions(
               Object.entries(cachedQuestion.problemoptions).some(
                  ([, { optiontext }]) => optiontext.length > 50
               )
            );
            const existingAttempt = Attempt?.find(
               (attempt) => attempt.problemId === cachedQuestion.problemid
            );
            setSelectedOption(existingAttempt?.option || []);
         } else if (question.length) {
            // Fallback to fetched data if not in cache
            const newQuestion = question.find(
               (q) => q.problemid === targetQuestionId
            );
            if (newQuestion) {
               console.log(
                  `🔄 Serving question ${targetQuestionId} from fetch (network load)`
               );
               markQuestionAccessed(targetQuestionId); // Mark as accessed for LRU tracking
               setQuestionData(newQuestion);
               setIsLongOptions(
                  Object.entries(newQuestion.problemoptions).some(
                     ([, { optiontext }]) => optiontext.length > 50
                  )
               );
               const existingAttempt = Attempt?.find(
                  (attempt) => attempt.problemId === newQuestion.problemid
               );
               setSelectedOption(existingAttempt?.option || []);
            }
         }
      }
   }, [
      start,
      question,
      switchQuestion,
      currentQuestionId,
      questionData?.problemid,
   ]);
   const size = 5; // Configurable size for better UI

   useEffect(() => {
      if (selectedOption.length) {
         // Get the elapsed time in proper format for storage
         if (!questionData) return;
         const elapsedTimeMs = getElapsedTime(questionData.problemid);
         const elapsedTimeSeconds = Math.floor(elapsedTimeMs / 1000);
         const timetakenFormatted =
            elapsedTimeSeconds >= 60
               ? `${Math.floor(elapsedTimeSeconds / 60)} min ${
                    elapsedTimeSeconds % 60
                 } sec`
               : `${elapsedTimeSeconds} sec`;

         if (Array.isArray(selectedOption)) {
            addAttempt({
               problemId: questionData.problemid,
               option: selectedOption,
               timetaken: timetakenFormatted,
               partialcorrectnessscore: null,
            });
         } else if (typeof selectedOption === "string") {
            addAttempt({
               problemId: questionData.problemid,
               option: [selectedOption as string],
               timetaken: timetakenFormatted,
               partialcorrectnessscore: null,
            });
         }
      }
   }, [selectedOption, questionData, getElapsedTime, addAttempt]);

   const handleOptionClick = useCallback(
      (option: string | string[]) => {
         const newOption = Array.isArray(option) ? option : [option];
         setSelectedOption(newOption);

         // Record the time when option is selected
         recordOptionSelectedTime(questionData.problemid);

         // Get the elapsed time in proper format for storage
         const elapsedTimeMs = getElapsedTime(questionData.problemid);
         const elapsedTimeSeconds = Math.floor(elapsedTimeMs / 1000);
         const timetakenFormatted =
            elapsedTimeSeconds >= 60
               ? `${Math.floor(elapsedTimeSeconds / 60)} min ${
                    elapsedTimeSeconds % 60
                 } sec`
               : `${elapsedTimeSeconds} sec`;

         addAttempt({
            problemId: questionData.problemid,
            option: newOption,
            timetaken: timetakenFormatted,
            partialcorrectnessscore: null,
         });
      },
      [questionData, addAttempt, recordOptionSelectedTime, getElapsedTime]
   );

   const handleClear = useCallback(() => {
      setSelectedOption([]);

      // Clear the recorded time from timer but keep timer running
      clearRecordedTime(questionData.problemid);

      // Remove the attempt for this question from the store
      const updatedAttempts = Attempt?.filter(
         (attempt) => attempt.problemId !== questionData.problemid
      );
      resetAttempt();
      if (updatedAttempts?.length) {
         updatedAttempts.forEach((attempt) => addAttempt(attempt));
      }
   }, [questionData, Attempt, resetAttempt, addAttempt, clearRecordedTime]);
   const flattenQuestionIds = () => {
      let ids: number[] = [];
      questionIds.forEach((id) => {
         if (typeof id === "number") {
            ids.push(id);
         } else {
            Object.values(id)[0].forEach((i) => {
               ids.push(i);
            });
         }
      });
      return ids;
   };
   const handlePageChange = (id: number) => {
      setSelectedOption([]);
      const newIndex = flattenQuestionIds().findIndex((q) => q === id);
      if (newIndex !== -1) {
         setStart(newIndex);
      }
   };

   const handleKeyNavigation = useCallback(
      (direction: "next" | "prev") => {
         const flattenedIds = flattenQuestionIds();
         const currentIndex = flattenedIds.findIndex(
            (id) => id === questionData?.problemid
         );

         if (currentIndex === -1) return;

         let newIndex: number;
         if (direction === "next") {
            newIndex =
               currentIndex < flattenedIds.length - 1 ? currentIndex + 1 : 0;
         } else {
            newIndex =
               currentIndex > 0 ? currentIndex - 1 : flattenedIds.length - 1;
         }

         const newQuestionId = flattenedIds[newIndex];
         handlePageChange(newQuestionId);
      },
      [questionData?.problemid, handlePageChange]
   );

   // Add keyboard event listener
   useEffect(() => {
      const handleKeyDown = (event: KeyboardEvent) => {
         // Only handle arrow keys when no input elements are focused
         const activeElement = document.activeElement as HTMLElement;
         if (
            activeElement?.tagName === "INPUT" ||
            activeElement?.tagName === "TEXTAREA" ||
            activeElement?.contentEditable === "true"
         ) {
            return;
         }

         switch (event.key) {
            case "ArrowLeft":
               event.preventDefault();
               handleKeyNavigation("prev");
               break;
            case "ArrowRight":
               event.preventDefault();
               handleKeyNavigation("next");
               break;
         }
      };

      document.addEventListener("keydown", handleKeyDown);
      return () => document.removeEventListener("keydown", handleKeyDown);
   }, [handleKeyNavigation]);

   const handleSubmit = async (callbackfn: () => void) => {
      // here we need to filter the empty attempts before submitting.
      const filteredAttempts =
         Attempt?.filter((attempt) => attempt.option.length > 0) || null;
      if (filteredAttempts?.length) {
         const result: questionResult[] = await submitUserAttempt({
            userId,
            Attempt: filteredAttempts,
         }) as any;
         setResultData(result);
      }
      callbackfn();
   };
   // Transform problemoptions to match the expected format
   const transformedOptions = questionData?.problemoptions.reduce(
      (acc, option, index) => {
         if (questionData.type.toLowerCase().includes("tc")) {
            acc[index.toString()] = {
               optiontext: option.optiontext,
               group: option.group,
            };
         } else {
            acc[index.toString()] = { optiontext: option.optiontext };
         }
         return acc;
      },
      {} as { [key: string]: { optiontext: string; group?: string } }
   );

   // Memoize header content separately for better performance
   const headerContent = React.useMemo(() => {
      return (
         <div className="absolute top-0 left-0 right-0 bg-background p-4 border-b z-10 flex items-center">
            <div className="flex items-center flex-grow overflow-hidden">
               <h1 className="text-xl font-semibold mr-3 text-foreground whitespace-nowrap">
                  Question:
               </h1>
               {questionData && (
                  <h2
                     className="text-lg text-muted-foreground truncate"
                     title={questionData.title}
                  >
                     {questionData.title}
                  </h2>
               )}
            </div>

            {/* Timer Component, Bookmark Button and Settings */}
            {questionData && (
               <div className="flex items-center gap-3 mr-4">
                  {showTimer && <Timer questionId={questionData.problemid} />}
                  {bookmarksEnabled && (
                     <button
                        onClick={() => toggleFlag(questionData.problemid)}
                        className={`p-2 rounded-lg transition-colors duration-200 ${
                           isFlagged(questionData.problemid)
                              ? "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 border border-yellow-300 dark:border-yellow-700"
                              : "bg-muted hover:bg-muted/80 text-muted-foreground border border-border"
                        }`}
                        title={
                           isFlagged(questionData.problemid)
                              ? "Remove bookmark"
                              : "Bookmark question"
                        }
                     >
                        <Bookmark
                           size={16}
                           className={
                              isFlagged(questionData.problemid)
                                 ? "fill-current"
                                 : ""
                           }
                        />
                     </button>
                  )}
                  {showTimer}
               </div>
            )}

            <button
               className="px-4 py-2 rounded-lg flex items-center gap-2 transition-colors duration-200 hover:bg-muted text-muted-foreground ml-4"
               onClick={() => {
                  handleSubmit(onClose);
               }}
            >
               {Attempt?.some((attempt) => attempt.option?.length > 0) ? (
                  <span className="text-base font-medium">Submit</span>
               ) : (
                  <span className="text-xl">×</span>
               )}
            </button>
         </div>
      );
   }, [
      questionData?.problemid,
      questionData?.title,
      bookmarksEnabled,
      isFlagged,
      toggleFlag,
      Attempt,
      handleSubmit,
      onClose,
   ]);

   return (
      <div className="fixed inset-0 flex items-center justify-center bg-black/50 backdrop-blur-sm z-50">
         <div className="relative bg-background p-6 shadow-xl rounded-lg w-11/12 max-w-5xl h-5/6 select-none overflow-hidden border border-border">
            {headerContent}
            <div className="h-full pt-16 overflow-auto">
               {status === "success" && questionData !== undefined ? (
                  <QuestionDisplay
                     key={questionData.problemid}
                     type={questionData.type}
                     title={questionData.title}
                     text={questionData.text}
                     options={transformedOptions}
                     selectedOption={selectedOption}
                     handleOptionClick={handleOptionClick}
                     isLongOptions={isLongOptions}
                     handleClear={handleClear}
                     handleClearAll={resetAttempt}
                     handlePageChange={handlePageChange}
                     questionIds={questionIds}
                     questionId={questionData.problemid}
                     size={size}
                     showPagination={true}
                     metadata={questionData.metadata}
                  />
               ) : (
                  <div className="flex items-center justify-center h-full">
                     <Loading size="lg" message="Loading question..." />
                  </div>
               )}
            </div>
         </div>
      </div>
   );
};

export default QuestionAttempt;
