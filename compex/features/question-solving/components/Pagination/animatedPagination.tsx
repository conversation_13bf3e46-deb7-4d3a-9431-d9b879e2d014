"use client";

import React, { useState, useEffect, useCallback, useMemo } from "react";
import { motion } from "framer-motion";
import { PaginationProps } from "@/features/question-solving/hooks/(definitions)/questionDefinition";
import { useAttemptStore } from "@/features/question-solving/hooks/(pagination)/problemDefinition";
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from "lucide-react";

interface AnimatedPaginationProps extends Omit<PaginationProps, 'size'> {
   windowSize?: number;
   size?: number; // Make size optional for backward compatibility
}

const AnimatedPagination: React.FC<AnimatedPaginationProps> = ({
   questionIds,
   currentQuestionId,
   onPageChange,
   windowSize = 9,
}) => {
   const { isFlagged, bookmarksEnabled } = useAttemptStore();
   const [isCyclicTransition, setIsCyclicTransition] = useState(false);

   // Memoize flatten function to prevent unnecessary recalculations
   const allQuestions = useMemo(() => {
      const flattened: number[] = [];
      questionIds.forEach((id) => {
         if (typeof id === "number") {
            flattened.push(id);
         } else {
            Object.values(id)[0].forEach((nestedId) => {
               flattened.push(nestedId);
            });
         }
      });
      return flattened;
   }, [questionIds]);

   const totalQuestions = allQuestions.length;
   const currentIndex = allQuestions.findIndex((id) => id === currentQuestionId);

   // Memoize window calculation to prevent unnecessary recalculations
   const { visibleQuestions, windowStart } = useMemo(() => {
      if (totalQuestions <= windowSize) {
         return { visibleQuestions: allQuestions, windowStart: 0 };
      }

      const halfWindow = Math.floor(windowSize / 2);
      
      // Ensure current question is centered when possible
      let start = currentIndex - halfWindow;
      
      // Adjust for beginning of list
      if (start < 0) {
         start = 0;
      }
      
      // Adjust for end of list
      if (start + windowSize > totalQuestions) {
         start = totalQuestions - windowSize;
      }

      return { 
         visibleQuestions: allQuestions.slice(start, start + windowSize),
         windowStart: start
      };
   }, [allQuestions, totalQuestions, currentIndex, windowSize]);
   
   // Track window changes for smooth sliding
   const [prevWindowStart, setPrevWindowStart] = useState(windowStart);
   const [slideDirection, setSlideDirection] = useState(0);
   const [isSliding, setIsSliding] = useState(false);

   useEffect(() => {
      if (prevWindowStart !== windowStart) {
         const direction = windowStart > prevWindowStart ? 1 : -1;
         setSlideDirection(direction);
         setIsSliding(true);
         
         // Reset after animation (increased from 400ms to 800ms)
         const timer = setTimeout(() => {
            setIsSliding(false);
            setPrevWindowStart(windowStart);
            setSlideDirection(0);
         }, 800);
         
         return () => clearTimeout(timer);
      }
   }, [windowStart, prevWindowStart]);

   // Memoized navigation handler to prevent unnecessary re-renders
   const navigate = useCallback((direction: "super-left" | "left" | "right" | "super-right") => {
      let newIndex: number;
      
      switch (direction) {
         case "super-left":
            newIndex = 0;
            break;
         case "left":
            if (currentIndex > 0) {
               newIndex = currentIndex - 1;
            } else {
               newIndex = totalQuestions - 1; // Cyclic: go to last question
               setIsCyclicTransition(true);
               setTimeout(() => setIsCyclicTransition(false), 800);
            }
            break;
         case "right":
            if (currentIndex < totalQuestions - 1) {
               newIndex = currentIndex + 1;
            } else {
               newIndex = 0; // Cyclic: go to first question
               setIsCyclicTransition(true);
               setTimeout(() => setIsCyclicTransition(false), 800);
            }
            break;
         case "super-right":
            newIndex = totalQuestions - 1;
            break;
         default:
            return;
      }

      onPageChange(allQuestions[newIndex]);
   }, [currentIndex, totalQuestions, currentQuestionId, allQuestions, onPageChange]);

   const handleQuestionClick = useCallback((questionId: number) => {
      onPageChange(questionId);
   }, [onPageChange]);

   // Memoize extended questions to prevent unnecessary recalculations
   const extendedQuestions = useMemo(() => {
      if (!isSliding) return visibleQuestions;
      
      // When sliding, show both old and new questions for smooth transition
      const prevStart = prevWindowStart;
      const currentStart = windowStart;
      
      if (slideDirection > 0) {
         // Sliding right - show questions from previous start to current end
         const start = Math.max(0, prevStart);
         const end = Math.min(totalQuestions, currentStart + windowSize);
         return allQuestions.slice(start, end);
      } else {
         // Sliding left - show questions from current start to previous end
         const start = Math.max(0, currentStart);
         const end = Math.min(totalQuestions, prevStart + windowSize);
         return allQuestions.slice(start, end);
      }
   }, [isSliding, visibleQuestions, prevWindowStart, windowStart, slideDirection, totalQuestions, allQuestions, windowSize]);

   const buttonSize = 44; // Fixed square size
   
   // Memoized font size calculator
   const getFontSize = useCallback((questionNumber: number) => {
      const length = questionNumber.toString().length;
      if (length <= 2) return "text-sm"; // 1-2 digits: normal size
      if (length === 3) return "text-xs"; // 3 digits: smaller
      return "text-[10px]"; // 4+ digits: very small
   }, []);
   
   // Memoized container offset calculation
   const containerOffset = useMemo(() => {
      if (!isSliding) return 0;
      
      const buttonWithGap = buttonSize + 4; // button size + gap
      
      if (slideDirection > 0) {
         // Sliding right - start from left
         return -(slideDirection * buttonWithGap);
      } else {
         // Sliding left - start from right
         return -(slideDirection * buttonWithGap);
      }
   }, [isSliding, slideDirection, buttonSize]);

   return (
      <div className="flex items-center justify-center space-x-2 py-4">
         {/* Super Left Button */}
         <motion.button
            onClick={() => navigate("super-left")}
            className="p-2 border rounded-lg bg-background border-border hover:bg-muted text-foreground transition-colors"
            disabled={currentIndex === 0}
            whileHover={{ 
               scale: 1.05,
               transition: {
                  type: "tween",
                  ease: [0.4, 0.0, 0.2, 1.0],
                  duration: 0.2
               }
            }}
            whileTap={{ 
               scale: 0.95,
               transition: {
                  type: "tween",
                  ease: [0.4, 0.0, 0.2, 1.0],
                  duration: 0.1
               }
            }}
         >
            <ChevronsLeft size={16} />
         </motion.button>

         {/* Left Button */}
         <motion.button
            onClick={() => navigate("left")}
            className="p-2 border rounded-lg bg-background border-border hover:bg-muted text-foreground transition-colors"
            whileHover={{ 
               scale: 1.05,
               transition: {
                  type: "tween",
                  ease: [0.4, 0.0, 0.2, 1.0],
                  duration: 0.2
               }
            }}
            whileTap={{ 
               scale: 0.95,
               transition: {
                  type: "tween",
                  ease: [0.4, 0.0, 0.2, 1.0],
                  duration: 0.1
               }
            }}
         >
            <ChevronLeft size={16} />
         </motion.button>

         {/* Question Number Buttons with Sliding Animation */}
         <div className="flex space-x-1 relative overflow-hidden" style={{ width: `${windowSize * (buttonSize + 4)}px` }}>
            {/* Cyclic transition indicator */}
            {isCyclicTransition && (
               <motion.div
                  className="absolute inset-0 border-2 border-primary rounded-lg pointer-events-none z-10"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ 
                     opacity: [0, 1, 1, 0], 
                     scale: [0.8, 1.1, 1.1, 1],
                     boxShadow: [
                        "0 0 0px rgba(59, 130, 246, 0)",
                        "0 0 20px rgba(59, 130, 246, 0.5)",
                        "0 0 20px rgba(59, 130, 246, 0.5)",
                        "0 0 0px rgba(59, 130, 246, 0)"
                     ]
                  }}
                  transition={{ duration: 0.8, ease: "easeInOut" }}
               />
            )}
            
            {/* Smooth sliding container without blinking */}
            <motion.div
               className="flex space-x-1"
               animate={{
                  x: containerOffset,
                  transition: {
                     type: "tween",
                     ease: [0.4, 0.0, 0.2, 1.0],
                     duration: 0.8
                  }
               }}
            >
               {extendedQuestions.map((questionId) => {
                  const isCurrentQuestion = questionId === currentQuestionId;
                  const isQuestionFlagged = bookmarksEnabled && isFlagged(questionId);
                  const isVisible = visibleQuestions.includes(questionId);
                  const questionPosition = allQuestions.findIndex(id => id === questionId) + 1;

                  return (
                     <motion.button
                        key={questionId}
                        onClick={() => handleQuestionClick(questionId)}
                        animate={{ 
                           scale: isVisible ? 1 : 0.9, 
                           opacity: isVisible ? 1 : 0.3,
                           transition: {
                              type: "tween",
                              ease: [0.4, 0.0, 0.2, 1.0],
                              duration: 0.6
                           }
                        }}
                        style={{ 
                           width: `${buttonSize}px`, 
                           height: `${buttonSize}px` 
                        }}
                        className={`
                           relative border rounded-lg font-medium transition-all duration-500 flex-shrink-0 flex items-center justify-center
                           ${getFontSize(questionPosition)}
                           ${isCurrentQuestion
                              ? "bg-primary text-primary-foreground border-primary shadow-lg"
                              : isQuestionFlagged
                              ? "bg-background border-2 border-yellow-400 dark:border-yellow-500 hover:bg-muted text-foreground"
                              : "bg-background border-border hover:bg-muted text-foreground"
                           }
                        `}
                        whileHover={{ 
                           scale: isCurrentQuestion ? 1.05 : 1.02,
                           transition: { 
                              type: "tween",
                              ease: [0.4, 0.0, 0.2, 1.0],
                              duration: 0.3 
                           }
                        }}
                        whileTap={{ 
                           scale: 0.95,
                           transition: {
                              type: "tween",
                              ease: [0.4, 0.0, 0.2, 1.0],
                              duration: 0.15
                           }
                        }}
                     >
                        {/* Flowing highlight effect */}
                        {isCurrentQuestion && (
                           <motion.div
                              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-lg"
                              initial={{ x: "-100%" }}
                              animate={{ x: "100%" }}
                              transition={{
                                 duration: 0.8,
                                 ease: [0.4, 0.0, 0.2, 1.0],
                                 repeat: Infinity,
                                 repeatDelay: 2,
                              }}
                           />
                        )}
                        
                        {/* Bookmark indicator */}
                        {isQuestionFlagged && (
                           <motion.div
                              className="absolute -top-1 -right-1 w-2 h-2 bg-yellow-500 rounded-full"
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              transition={{ 
                                 type: "tween",
                                 ease: [0.4, 0.0, 0.2, 1.0],
                                 duration: 0.3 
                              }}
                           />
                        )}
                        
                        <span className="relative z-10">{questionPosition}</span>
                     </motion.button>
                  );
               })}
            </motion.div>
         </div>

         {/* Right Button */}
         <motion.button
            onClick={() => navigate("right")}
            className="p-2 border rounded-lg bg-background border-border hover:bg-muted text-foreground transition-colors"
            whileHover={{ 
               scale: 1.05,
               transition: {
                  type: "tween",
                  ease: [0.4, 0.0, 0.2, 1.0],
                  duration: 0.2
               }
            }}
            whileTap={{ 
               scale: 0.95,
               transition: {
                  type: "tween",
                  ease: [0.4, 0.0, 0.2, 1.0],
                  duration: 0.1
               }
            }}
         >
            <ChevronRight size={16} />
         </motion.button>

         {/* Super Right Button */}
         <motion.button
            onClick={() => navigate("super-right")}
            className="p-2 border rounded-lg bg-background border-border hover:bg-muted text-foreground transition-colors"
            disabled={currentIndex === totalQuestions - 1}
            whileHover={{ 
               scale: 1.05,
               transition: {
                  type: "tween",
                  ease: [0.4, 0.0, 0.2, 1.0],
                  duration: 0.2
               }
            }}
            whileTap={{ 
               scale: 0.95,
               transition: {
                  type: "tween",
                  ease: [0.4, 0.0, 0.2, 1.0],
                  duration: 0.1
               }
            }}
         >
            <ChevronsRight size={16} />
         </motion.button>
      </div>
   );
};

export default AnimatedPagination;