import React, { useState, useEffect } from "react";
import {
   HybridProblem,
   ProblemsSet,
   Problem,
   ProblemsResponse,
} from "@/features/question-solving/hooks/(pagination)/problemDefinition";
import Pagination from "@/shared/components/ui/pagination";
import {
   useProblemForPaginationStore,
   questionList,
} from "@/features/question-solving/hooks/(pagination)/problemDefinition";
import ArrowIcons from "@/shared/components/ui/icons/arrow-icons";
import {
   Table,
   TableBody,
   TableCell,
   TableHead,
   TableHeader,
   TableRow,
} from "@/shared/components/ui/table";
import { Button } from "@/shared/components/ui/button";

interface QueryDataProp {
   status: string;
   error: unknown;
   data: ProblemsResponse | undefined;
   previousData: ProblemsResponse | null;
}

const QuestionTable = React.memo(function QuestionTable({
   queryData,
   openQuestion,
   displaySolvedQuestions,
   showDifficulty = true,
}: {
   queryData: QueryDataProp;
   openQuestion: () => void;
   displaySolvedQuestions: boolean;
   showDifficulty?: boolean;
}) {
   const [rows, setRows] = useState<HybridProblem[]>([]);
   function isProblemSet(item: HybridProblem) {
      if ("isExpanded" in item) {
         return true;
      } else {
         return false;
      }
   }
   useEffect(() => {
      if (queryData.data?.problemData) {
         setRows(queryData.data.problemData);
      }
   }, [queryData.data]);

   const toggleExpand = (index: number) => {
      const updatedRows = [...rows];
      if ("isExpanded" in updatedRows[index])
         updatedRows[index].isExpanded = !updatedRows[index].isExpanded;
      setRows(updatedRows);
   };

   const { status, error, data } = queryData;
   const {
      categoryIndex,
      sortOrder,
      page,
      pageSize,
      setCategoryIndex,
      setSortOrder,
   } = useProblemForPaginationStore();
   const { start, setStart } = questionList();

   const headings = showDifficulty ? ["#", "Title", "Difficulty", "Added on"] : ["#", "Title", "Added on"];

   const handleSortClick = (
      category: "#" | "Title" | "Difficulty" | "Added on"
   ) => {
      if (category === "#") {
         return;
      }
      if (categoryIndex === headings.indexOf(category)) {
         if (sortOrder === 0) {
            setSortOrder(1);
         } else if (sortOrder === 1) {
            setSortOrder(-1);
         } else {
            setSortOrder(0);
         }
      } else {
         setCategoryIndex(headings.indexOf(category));
         setSortOrder(1);
      }
   };

   const renderSortArrows = (
      category: "#" | "Title" | "Difficulty" | "Added on"
   ) => {
      if (category === "#") {
         return <></>;
      }
      if (categoryIndex !== headings.indexOf(category)) {
         return <ArrowIcons up down />;
      }
      if (sortOrder === 1) {
         return <ArrowIcons up />;
      }
      if (sortOrder === -1) {
         return <ArrowIcons down />;
      }
      return <ArrowIcons up down />;
   };

   const onRowClick = (absoluteIndex: number) => {
      // Use the absoluteIndex directly from the problem/problemSet data
      setStart(absoluteIndex);
   };
   useEffect(() => {
      if (start >= 0) {
         openQuestion();
      }
   }, [start]);

   // Function to determine the background color based on difficulty level
   const getDifficultyColor = (difficulty: number) => {
      switch (difficulty) {
         case 1:
            return "bg-red-100 dark:bg-red-900/30 text-foreground border border-border";
         case 2:
            return "bg-red-200 dark:bg-red-800/30 text-foreground border border-border";
         case 3:
            return "bg-red-300 dark:bg-red-700/30 text-foreground border border-border";
         case 4:
            return "bg-red-400 dark:bg-red-600/30 text-foreground border border-border";
         case 5:
            return "bg-red-500 dark:bg-red-500/30 text-foreground border border-border";
         default:
            return "bg-red-200 dark:bg-red-800/30 text-foreground border border-border";
      }
   };
   // function to show the date
   function formatDate(isoDateString: string) {
      const date = new Date(isoDateString);
      const day = String(date.getUTCDate()).padStart(2, "0");
      const month = String(date.getUTCMonth() + 1).padStart(2, "0"); // Months are 0-indexed
      const year = date.getUTCFullYear();
      return `${day}/${month}/${year}`;
   }
   // Early return for null/undefined data - let parent handle loading states
   if (queryData === undefined || !queryData.data) {
      return null;
   }

   // Let parent DataLoader handle loading and error states
   if (status === "loading" || error) {
      return null;
   }

   if (status === "success") {
      return (
         <div className="space-y-4">
            <div className="rounded-md border border-border">
               <Table>
                  <TableHeader>
                     <TableRow className="hover:bg-muted/50">
                        {headings.map((heading) => (
                           <TableHead key={heading}>
                              <Button
                                 variant="ghost"
                                 className="w-full justify-between text-foreground"
                                 onClick={() =>
                                    handleSortClick(
                                       heading as
                                          | "Title"
                                          | "Difficulty"
                                          | "Added on"
                                    )
                                 }
                              >
                                 {heading}
                                 {renderSortArrows(
                                    heading as
                                       | "Title"
                                       | "Difficulty"
                                       | "Added on"
                                 )}
                              </Button>
                           </TableHead>
                        ))}
                     </TableRow>
                  </TableHeader>
                  <TableBody>
                     {rows.length > 0 ? (
                        rows.map((row, index) => {
                           return (
                              <React.Fragment key={`parent-${index}`}>
                                 <TableRow
                                    className={`hover:bg-muted/50 ${
                                       isProblemSet(row) ? "bg-muted/20" : ""
                                    } ${
                                       displaySolvedQuestions &&
                                       (isProblemSet(row)
                                          ? false // Don't apply solved styling to parent rows
                                          : (row as Problem).iscorrect !==
                                               null &&
                                            (row as Problem).iscorrect !==
                                               undefined
                                          ? (row as Problem).iscorrect === true
                                             ? "bg-green-500/10 dark:bg-green-500/20"
                                             : "bg-red-500/10 dark:bg-red-500/20"
                                          : "")
                                    }`}
                                    onClick={() => {
                                       if (isProblemSet(row)) {
                                          return toggleExpand(index);
                                       } else {
                                          return onRowClick((row as Problem).absoluteIndex || 0);
                                       }
                                    }}
                                 >
                                    <TableCell
                                       align="center"
                                       className="text-foreground"
                                    >
                                       {index + 1}
                                    </TableCell>
                                    <TableCell className="text-foreground">
                                       {isProblemSet(row) && (
                                          <span className="mr-2 text-foreground">
                                             {(row as ProblemsSet).isExpanded
                                                ? "▼"
                                                : "▶"}
                                          </span>
                                       )}
                                       {row.title}
                                    </TableCell>
                                    {showDifficulty && (
                                       <TableCell align="center">
                                          <span
                                             className={`inline-block px-3 py-1 rounded-full ${getDifficultyColor(
                                                (row as Problem).difficulty
                                             )}`}
                                          >
                                             {(row as Problem).difficulty}
                                          </span>
                                       </TableCell>
                                    )}
                                    <TableCell className="text-foreground">
                                       {isProblemSet(row)
                                          ? formatDate(
                                               (row as ProblemsSet).problems[0]
                                                  .addedDate
                                            )
                                          : formatDate(
                                               (row as Problem).addedDate
                                            )}
                                    </TableCell>
                                 </TableRow>
                                 {isProblemSet(row) &&
                                    (row as ProblemsSet).isExpanded && (
                                       <>
                                          {(row as ProblemsSet).problems?.map(
                                             (child, childIdx) => (
                                                <TableRow
                                                   key={`child-${index}-${childIdx}`}
                                                   className={`hover:bg-muted/50 bg-muted/10 ${
                                                      displaySolvedQuestions &&
                                                      child.iscorrect !==
                                                         null &&
                                                      child.iscorrect !==
                                                         undefined
                                                         ? child.iscorrect ===
                                                           true
                                                            ? "bg-green-500/10 dark:bg-green-500/20"
                                                            : "bg-red-500/10 dark:bg-red-500/20"
                                                         : ""
                                                   }`}
                                                   onClick={() =>
                                                      onRowClick(child.absoluteIndex || 0)
                                                   }
                                                >
                                                   <TableCell className="text-foreground">
                                                      <span className="ml-4">
                                                         └─
                                                      </span>
                                                      {`${index + 1}.${
                                                         childIdx + 1
                                                      }`}
                                                   </TableCell>
                                                   <TableCell className="text-foreground">
                                                      <span className="ml-4">
                                                         {child.title}
                                                      </span>
                                                   </TableCell>
                                                   {showDifficulty && (
                                                      <TableCell align="center">
                                                         <span
                                                            className={`inline-block px-3 py-1 rounded-full ${getDifficultyColor(
                                                               child.difficulty
                                                            )}`}
                                                         >
                                                            {child.difficulty}
                                                         </span>
                                                      </TableCell>
                                                   )}
                                                   <TableCell className="text-foreground">
                                                      {formatDate(
                                                         child.addedDate
                                                      )}
                                                   </TableCell>
                                                </TableRow>
                                             )
                                          )}
                                       </>
                                    )}
                              </React.Fragment>
                           );
                        })
                     ) : (
                        <TableRow>
                           <TableCell
                              colSpan={showDifficulty ? 4 : 3}
                              className="text-center py-4 text-muted-foreground"
                           >
                              Content not available
                           </TableCell>
                        </TableRow>
                     )}
                  </TableBody>
               </Table>
            </div>
            <Pagination totalProblems={data?.totalProblems} />
         </div>
      );
   }
});

export default QuestionTable;
