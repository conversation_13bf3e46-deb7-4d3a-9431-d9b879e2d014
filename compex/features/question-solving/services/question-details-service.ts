import { prisma } from "@/shared/lib/configs/prisma";

/**
 * Service for fetching detailed question information
 * 
 * <PERSON>les retrieval of question details including options, metadata,
 * and parent problem set information for child questions.
 */

/**
 * Detailed question data with options and metadata
 */
export interface QuestionDetails {
   type: string;
   problemid: number;
   title: string;
   text: string;
   metadata: any;
   isChildren: boolean;
   problemsSetId: number | null;
   problemoptions: {
      optiontext: string;
      group: string | null;
   }[];
}

/**
 * Fetches detailed information for multiple questions
 * 
 * Retrieves question text, options, metadata, and handles special cases:
 * - For child questions, fetches parent problem set information
 * - For NE (Numeric Entry) questions, clears metadata to hide answers
 * 
 * @param questionIds - Array of question IDs to fetch details for
 * @returns Promise resolving to array of detailed question data
 */
export async function getQuestionDetails(questionIds: number[]): Promise<QuestionDetails[]> {
   // Fetch questions with optimized select
   const questions = await prisma.problems.findMany({
      where: {
         problemid: {
            in: questionIds,
         },
      },
      select: {
         type: true,
         problemid: true,
         title: true,
         text: true,
         metadata: true,
         isChildren: true,
         problemsSetId: true,
         problemoptions: {
            select: {
               optiontext: true,
               group: true,
            },
            orderBy: {
               optionid: "asc",
            },
         },
      },
   });

   // Process each question to handle special cases
   const processedQuestions = await Promise.all(
      questions.map(async (question) => {
         // Handle child questions - fetch parent metadata
         if (question.isChildren && question.problemsSetId) {
            const parentProblem = await prisma.problemsSet.findFirst({
               where: {
                  problemsSetId: question.problemsSetId,
               },
               select: {
                  problemsSetId: true,
                  title: true,
                  content: true,
               },
            });
            
            if (parentProblem) {
               question.metadata = { ...parentProblem };
            }
         }

         // Handle NE (Numeric Entry) questions - clear metadata to hide answers
         if (question.type === "NE") {
            question.metadata = {};
         }

         return question as QuestionDetails;
      })
   );

   return processedQuestions;
}