import { prisma } from "@/shared/lib/configs/prisma";


export async function getProblemsSetForPagination(
   userId: number,
   examType: number,
   section: number,
   tags: string[] | null = null
) {
   const whereClause = {
      examtypeid: examType,
      sectionid: section,
      ...(tags
         ? {
              problemssettags: {
                 some: {
                    tags: {
                       name: {
                          in: tags,
                       },
                    },
                 },
              },
           }
         : {}),
   };
   const problemSetData = await prisma.problemsSet.findMany({
      where: whereClause,
      select: {
         problemsSetId: true,
         title: true,
      },
   });
   // Get all problems associated with each problem set
   const problemSetWithProblems = await Promise.all(
      problemSetData.map(async (problemSet) => {
         const problems = await prisma.problems.findMany({
            where: {
               problemsSetId: problemSet.problemsSetId,
            },
            select: {
               problemid: true,
               title: true,
               difficulty: true,
               addedDate: true,
            },
         });
         return {
            ...problemSet,
            problems,
         };
      })
   );

   const allProblemIds = problemSetWithProblems.flatMap((ps) =>
      ps.problems.map((p) => p.problemid)
   );

   const userAttempts = await prisma.userattempts.findMany({
      where: {
         userid: userId,
         problemid: {
            in: allProblemIds,
         },
      },
      select: {
         problemid: true,
         iscorrect: true,
      },
   });

   const problemDataMap = new Map(
      userAttempts.map((attempt) => [attempt.problemid, attempt.iscorrect])
   );

   const problemSetWithProblemsAndStatus = problemSetWithProblems.map(
      (problemSet) => ({
         ...problemSet,
         problems: problemSet.problems.map((problem) => ({
            ...problem,
            iscorrect: problemDataMap.get(problem.problemid) || null,
         })),
      })
   );

   const totalProblemSets = await prisma.problemsSet.count({
      where: whereClause,
   });

   return {
      problemSetWithProblems: problemSetWithProblemsAndStatus,
      totalProblemSets,
   };
}