import { prisma } from "@/shared/lib/configs/prisma";

/**
 * Service for handling question pagination and filtering
 * 
 * Manages the complex logic of fetching problems and problem sets
 * with user attempt data for pagination display.
 */

/**
 * Interface for paginated problem data
 */
export interface PaginatedProblemData {
   problemData: (ProblemWithAttempt | ProblemSetWithAttempts)[];
   totalProblems: number;
}

/**
 * Individual problem with user attempt status
 */
export interface ProblemWithAttempt {
   problemid: number;
   title: string;
   difficulty: number | null;
   addedDate: Date | null;
   type: string | null;
   problemsSetId: number | null;
   iscorrect: boolean | null;
}

/**
 * Problem set with child problems and user attempt status
 */
export interface ProblemSetWithAttempts {
   problemsSetId: number;
   title: string;
   isExpanded: boolean;
   problems: ProblemWithAttempt[];
}

/**
 * Fetches problems and problem sets for pagination with user attempt data
 * 
 * This function handles complex filtering by exam type, section, and tags,
 * while efficiently fetching user attempt data for all problems.
 * 
 * @param userId - The ID of the user to fetch attempts for
 * @param examType - The exam type ID to filter by
 * @param section - The section ID to filter by
 * @param tags - Optional array of tag names to filter by
 * @returns Promise resolving to paginated problem data with attempt status
 */
export async function getProblemsForPagination(
   userId: number,
   examType: number,
   section: number,
   tags: string[] | null = null
): Promise<PaginatedProblemData> {
   // Fetch top-level problems with optimized query
   const problems = await prisma.problems.findMany({
      where: {
         examtypeid: examType,
         sectionid: section,
         isChildren: false,
         type: {
            not: "",
         },
         ...(tags
            ? {
                 problemtags: {
                    some: {
                       tags: {
                          name: {
                             in: tags,
                          },
                       },
                    },
                 },
              }
            : {}),
      },
      select: {
         problemid: true,
         title: true,
         difficulty: true,
         addedDate: true,
         type: true,
         problemsSetId: true,
      },
   });

   // Fetch problem sets with child problems
   const problemSets = await prisma.problemsSet.findMany({
      where: {
         examtypeid: examType,
         sectionid: section,
         ...(tags
            ? {
                 problems: {
                    some: {
                       problemtags: {
                          some: {
                             tags: {
                                name: {
                                   in: tags,
                                },
                             },
                          },
                       },
                    },
                 },
              }
            : {}),
      },
      select: {
         problemsSetId: true,
         title: true,
         problems: {
            select: {
               problemid: true,
               title: true,
               difficulty: true,
               addedDate: true,
               type: true,
            },
         },
      },
   });

   // Collect all problem IDs for user attempts query
   const allProblemIds = [
      ...problems.map((p) => p.problemid),
      ...problemSets.flatMap((ps) => ps.problems.map((p) => p.problemid)),
   ];

   // Fetch user attempts efficiently
   const userAttempts = await prisma.userattempts.findMany({
      where: {
         userid: userId,
         problemid: {
            in: allProblemIds,
         },
      },
      select: {
         problemid: true,
         iscorrect: true,
      },
   });

   // Create lookup map for O(1) attempt status retrieval
   const problemStatusMap = new Map(
      userAttempts.map((attempt) => [attempt.problemid, attempt.iscorrect])
   );

   // Enrich problems with attempt status
   const enrichedProblems: ProblemWithAttempt[] = problems.map((problem) => ({
      ...problem,
      iscorrect: problemStatusMap.get(problem.problemid) ?? null,
   }));

   // Enrich problem sets with attempt status for child problems
   const enrichedProblemSets: ProblemSetWithAttempts[] = problemSets.map((problemSet) => {
      const enrichedChildProblems = problemSet.problems.map((childProblem) => ({
         ...childProblem,
         problemsSetId: problemSet.problemsSetId,
         iscorrect: problemStatusMap.get(childProblem.problemid) ?? null,
      }));

      return {
         problemsSetId: problemSet.problemsSetId,
         title: problemSet.title,
         isExpanded: false,
         problems: enrichedChildProblems,
      };
   });

   // Combine and sort by date
   const combinedProblemData = [...enrichedProblems, ...enrichedProblemSets];
   combinedProblemData.sort((a, b) => {
      const dateA = getComparisonDate(a);
      const dateB = getComparisonDate(b);
      return dateA.getTime() - dateB.getTime();
   });

   const totalProblems = problems.length + problemSets.length;

   return { problemData: combinedProblemData, totalProblems };
}

/**
 * Helper function to get comparison date for sorting
 * 
 * @param item - Problem or problem set to extract date from
 * @returns Date object for comparison
 */
function getComparisonDate(item: ProblemWithAttempt | ProblemSetWithAttempts): Date {
   if ("addedDate" in item && item.addedDate) {
      return new Date(item.addedDate);
   }
   
   if ("problems" in item && item.problems[0]?.addedDate) {
      return new Date(item.problems[0].addedDate);
   }
   
   return new Date(0); // Fallback to epoch
}