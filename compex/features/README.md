# Features Directory

This directory contains the core business features of the application, organized by domain. Each feature is self-contained with its own components, services, hooks, and types.

## Structure

* **exam-management**: Exam types, sections, and tags management
* **question-solving**: Question display, templates, and navigation
* **user-analytics**: Performance tracking, timers, and results
* **user-management**: Authentication and user profiles

## Feature Organization

Each feature follows a consistent structure:

```
features/[feature-name]/
├── components/       # UI components specific to this feature
├── services/         # Business logic and API interactions
├── hooks/            # React hooks for state and effects
├── stores/           # State management (Zustand stores)
├── types/            # TypeScript type definitions
└── index.ts          # Public exports
```

## Integration with Other Folders

- Components are used by pages in `/app`
- Services interact with API routes in `/app/api`
- Shared UI components from `/shared/components` are used
- Database models from `/prisma` are referenced in types

## Key Features

### Exam Management

Handles the organization of exams, sections, and tags:
- Exam type selection
- Section filtering
- Tag management

### Question Solving

Core question display and interaction:
- Question rendering with different templates
- Answer submission
- Solution display
- Navigation between questions

### User Analytics

Tracks and displays user performance:
- Performance metrics calculation
- Progress tracking
- Results visualization