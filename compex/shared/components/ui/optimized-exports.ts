/**
 * Optimized component exports for better tree-shaking
 * 
 * This file provides specific exports instead of barrel exports
 * to improve bundle size optimization and tree-shaking effectiveness.
 * 
 * Instead of using `export * from "./components"`, we explicitly export
 * only the components that are actually used, reducing bundle size.
 */

// Core UI components (most frequently used)
export { <PERSON><PERSON> } from "./button";
export { Label } from "./label";
export { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./select";
export { Switch } from "./switch";
export { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "./table";

// Layout and structure components
export { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "./card";

// Feedback and loading components
export { Alert, AlertDescription, AlertTitle } from "./alert";
export { Badge } from "./badge";
export { Skeleton } from "./skeleton";

// Overlay and popup components
export { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "./tooltip";

/**
 * Component categories for conditional imports
 * 
 * These allow importing only specific categories of components
 * when working with particular features.
 */

// Note: Component category objects have been removed to avoid
// import/export complexity during the migration to focused stores.
// Use direct imports from specific component files instead.