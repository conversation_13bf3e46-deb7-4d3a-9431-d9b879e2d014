"use client";

import { useEffect } from 'react';
import { initWebVitals, setupCustomPerformanceObserver } from '@/shared/lib/performance/web-vitals';

/**
 * Props for WebVitalsTracker component
 */
interface WebVitalsTrackerProps {
   /** Enable analytics reporting (default: production only) */
   enableAnalytics?: boolean;
   /** Enable console logging (default: development only) */
   enableConsoleLog?: boolean;
   /** Custom endpoint for sending metrics */
   endpoint?: string;
   /** Sample rate for metric collection (0-1) */
   sampleRate?: number;
   /** Enable custom performance observers */
   enableCustomObservers?: boolean;
}

/**
 * WebVitalsTracker - Client-side component for monitoring Core Web Vitals
 * 
 * This component initializes performance monitoring and should be placed
 * in the root layout or main app component.
 * 
 * @example
 * ```tsx
 * // In layout.tsx or app.tsx
 * <WebVitalsTracker 
 *    enableAnalytics={true}
 *    endpoint="/api/metrics"
 *    sampleRate={0.1}
 * />
 * ```
 */
export function WebVitalsTracker({
   enableAnalytics,
   enableConsoleLog,
   endpoint,
   sampleRate = 1.0,
   enableCustomObservers = true,
}: WebVitalsTrackerProps) {
   useEffect(() => {
      // Initialize Web Vitals monitoring
      initWebVitals({
         enableAnalytics,
         enableConsoleLog,
         endpoint,
         sampleRate,
      });

      // Set up custom performance observers
      if (enableCustomObservers) {
         setupCustomPerformanceObserver();
      }

      // Performance budget warning (development only)
      if (process.env.NODE_ENV === 'development') {
         setTimeout(() => {
            if (typeof window !== 'undefined' && 'performance' in window) {
               const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
               if (navigation) {
                  const pageLoadTime = navigation.loadEventEnd - navigation.requestStart;
                  if (pageLoadTime > 3000) {
                     console.warn(`Page load time (${Math.round(pageLoadTime)}ms) exceeds recommended threshold (3000ms)`);
                  }
               }
            }
         }, 2000);
      }

   }, [enableAnalytics, enableConsoleLog, endpoint, sampleRate, enableCustomObservers]);

   // This component doesn't render anything
   return null;
}

/**
 * Display current Web Vitals metrics (development only)
 * 
 * Shows a floating debug panel with current performance metrics
 */
export function WebVitalsDebugPanel() {
   useEffect(() => {
      if (process.env.NODE_ENV !== 'development') {
         return;
      }

      let metrics: Record<string, number> = {};

      const updateDisplay = () => {
         const existingPanel = document.getElementById('web-vitals-debug');
         if (existingPanel) {
            existingPanel.remove();
         }

         const panel = document.createElement('div');
         panel.id = 'web-vitals-debug';
         panel.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 9999;
            max-width: 200px;
         `;

         const metricsHtml = Object.entries(metrics)
            .map(([name, value]) => {
               const rating = getRating(name, value);
               const color = rating === 'good' ? '#4ade80' : rating === 'needs-improvement' ? '#fbbf24' : '#f87171';
               return `<div style="color: ${color}">${name}: ${Math.round(value * (name === 'CLS' ? 1000 : 1))}${name === 'CLS' ? '' : 'ms'}</div>`;
            })
            .join('');

         panel.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 5px;">Web Vitals</div>
            ${metricsHtml}
            <div style="margin-top: 5px; font-size: 10px; opacity: 0.7;">Dev Only</div>
         `;

         document.body.appendChild(panel);
      };

      const getRating = (name: string, value: number) => {
         const thresholds: Record<string, { good: number; poor: number }> = {
            CLS: { good: 0.1, poor: 0.25 },
            FID: { good: 100, poor: 300 },
            FCP: { good: 1800, poor: 3000 },
            LCP: { good: 2500, poor: 4000 },
            TTFB: { good: 800, poor: 1800 },
         };

         const threshold = thresholds[name];
         if (!threshold) return 'good';
         if (value <= threshold.good) return 'good';
         if (value <= threshold.poor) return 'needs-improvement';
         return 'poor';
      };

      // Listen for custom metric events
      const handleMetric = (event: CustomEvent) => {
         metrics[event.detail.name] = event.detail.value;
         updateDisplay();
      };

      window.addEventListener('web-vitals-metric' as any, handleMetric);

      // Initial display
      updateDisplay();

      return () => {
         window.removeEventListener('web-vitals-metric' as any, handleMetric);
         const panel = document.getElementById('web-vitals-debug');
         if (panel) {
            panel.remove();
         }
      };
   }, []);

   return null;
}