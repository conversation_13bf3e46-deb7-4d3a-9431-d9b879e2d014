// components/Header.tsx

import clsx from "clsx";
import React from "react";

interface HeaderProps {
   leftItems?: React.ReactNode[];
   centerItems?: React.ReactNode[];
   rightItems?: React.ReactNode[];
   gap?: number;
   className?: string;
}

const Header: React.FC<HeaderProps> = ({
   leftItems = [],
   centerItems = [],
   rightItems = [],
   gap = 5,
   className = "",
}) => {
   return (
      <header
         className={clsx(
            "flex justify-between items-center py-4 px-6 shadow-md mb-4",
            className,
            gap
         )}
      >
         <div className="flex items-center space-x-4 gap-3">
            {leftItems.map((item, index) => (
               <div key={index}>{item}</div>
            ))}
         </div>
         <div className="flex items-center space-x-4 gap-3">
            {centerItems.map((item, index) => (
               <div key={index}>{item}</div>
            ))}
         </div>
         <div className="flex items-center space-x-4 gap-3">
            {rightItems.map((item, index) => (
               <div key={index}>{item}</div>
            ))}
         </div>
      </header>
   );
};

export default Header;
