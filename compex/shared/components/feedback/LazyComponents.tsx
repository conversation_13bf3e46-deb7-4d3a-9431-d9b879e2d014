"use client";

import { lazy } from "react";
import LazyLoader from "./LazyLoader";
import { 
   TagSectionSkeleton, 
   QuestionTableSkeleton, 
   CalendarSkeleton,
   ContentSkeleton 
} from "./SkeletonLoaders";

// Lazy load individual components with their respective skeletons
export const LazyCalendar = (props: any) => (
   <LazyLoader
      loader={() => import("@/shared/components/ui/calendar")}
      fallback={<CalendarSkeleton />}
      loadingMessage="Loading calendar..."
      props={props}
   />
);

export const LazyTagSection = (props: any) => (
   <LazyLoader
      loader={() => import("@/features/exam-management/components/tag-properties")}
      fallback={<TagSectionSkeleton />}
      loadingMessage="Loading categories..."
      props={props}
   />
);

export const LazyQuestionTable = (props: any) => (
   <LazyLoader
      loader={() => import("@/features/question-solving/components/question-table/question-table")}
      fallback={<QuestionTableSkeleton />}
      loadingMessage="Loading questions..."
      props={props}
   />
);

export const LazyQuestionWindow = (props: any) => (
   <LazyLoader
      loader={() => import("@/features/question-solving/components/QuestionWindow/questionwindow")}
      fallback={
         <div className="fixed inset-0 flex items-center justify-center bg-black/50 backdrop-blur-sm z-50">
            <div className="relative bg-background p-6 shadow-xl rounded-lg w-11/12 max-w-5xl h-5/6 select-none overflow-hidden border border-border">
               <ContentSkeleton rows={8} className="h-full p-4" />
            </div>
         </div>
      }
      loadingMessage="Loading question..."
      props={props}
   />
);

export const LazyResultWindow = (props: any) => (
   <LazyLoader
      loader={() => import("@/features/user-analytics/components/result-window/resultWindow")}
      fallback={
         <div className="fixed inset-0 flex items-center justify-center bg-black/50 backdrop-blur-sm z-50">
            <div className="relative bg-background p-6 shadow-xl rounded-lg w-11/12 max-w-4xl h-4/5 select-none overflow-hidden border border-border">
               <ContentSkeleton rows={6} className="h-full p-4" />
            </div>
         </div>
      }
      loadingMessage="Loading results..."
      props={props}
   />
);

// Higher-order component for lazy loading with error boundaries
export const withLazyLoading = <T extends object>(
   componentLoader: () => Promise<{ default: React.ComponentType<T> }>,
   fallback?: React.ReactNode,
   loadingMessage?: string
) => {
   const LazyComponent = (props: T) => (
      <LazyLoader
         loader={componentLoader}
         fallback={fallback}
         loadingMessage={loadingMessage}
         props={props}
         errorBoundary={true}
      />
   );
   LazyComponent.displayName = `LazyComponent`;
   return LazyComponent;
};

// Preload function for critical components
export const preloadComponent = (
   componentLoader: () => Promise<{ default: React.ComponentType<any> }>
) => {
   const component = lazy(componentLoader);
   // Trigger the lazy loading immediately
   componentLoader().catch(console.error);
   return component;
};

// Utility function to batch preload multiple components
export const preloadComponents = (
   loaders: Array<() => Promise<{ default: React.ComponentType<any> }>>
) => {
   return Promise.allSettled(
      loaders.map(loader => loader().catch(console.error))
   );
};

export default LazyLoader;