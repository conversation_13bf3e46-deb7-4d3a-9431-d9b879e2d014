// app/ui/fonts.ts

import {
   <PERSON><PERSON>_Slab,
   <PERSON><PERSON><PERSON><PERSON>,
   <PERSON><PERSON>,
   Montserrat,
   <PERSON>pin<PERSON>,
   <PERSON><PERSON><PERSON>,
   Roboto,
   Open_Sans,
   Lato,
   Nuni<PERSON>,
   PT_Sans,
   Source_Sans_3,
} from "@next/font/google";

const RobotoSlab = Roboto_Slab({
   subsets: ["latin"],
   display: "swap",
   variable: "--font-primary-1",
});

const MerriweatherFont = Merriweather({
   subsets: ["latin"],
   display: "swap",
   variable: "--font-primary-2",
   weight: "300",
});

const LoraFont = Lora({
   subsets: ["latin"],
   display: "swap",
   variable: "--font-primary-3",
});

const MontserratFont = Montserrat({
   subsets: ["latin"],
   display: "swap",
   variable: "--font-secondary-1",
});

const PoppinsFont = Poppins({
   subsets: ["latin"],
   display: "swap",
   variable: "--font-secondary-2",
   weight: "300",
});

const <PERSON><PERSON><PERSON>Font = <PERSON><PERSON><PERSON>({
   subsets: ["latin"],
   display: "swap",
   variable: "--font-secondary-3",
});

const RobotoFont = Roboto({
   subsets: ["latin"],
   display: "swap",
   variable: "--font-body-1",
   weight: "100",
});

const OpenSansFont = Open_Sans({
   subsets: ["latin"],
   display: "swap",
   variable: "--font-body-2",
});

const LatoFont = Lato({
   subsets: ["latin"],
   display: "swap",
   variable: "--font-body-3",
   weight: "100",
});

const NunitoFont = Nunito({
   subsets: ["latin"],
   display: "swap",
   variable: "--font-accent-1",
});

const PTSansFont = PT_Sans({
   subsets: ["latin"],
   display: "swap",
   variable: "--font-accent-2",
   weight: "400",
});

const SourceSansProFont = Source_Sans_3({
   subsets: ["latin"],
   display: "swap",
   variable: "--font-accent-3",
});

export const Pfonts = [RobotoSlab, MerriweatherFont, LoraFont];
export const Sfonts = [MontserratFont, PoppinsFont, RalewayFont];
export const Bfonts = [RobotoFont, OpenSansFont, LatoFont];
export const Afonts = [NunitoFont, PTSansFont, SourceSansProFont];
