import { create } from "zustand";

/**
 * Problem and ProblemsSet data structures
 */
export interface Problem {
  problemid: string;
  title: string;
  difficulty: number;
  addedDate: string;
  problemtags: { tags: { name: string | null } }[];
  iscorrect: boolean | null | undefined;
  absoluteIndex?: number;
}

export interface ProblemsSet {
  problemsSetId: number;
  title: string;
  isExpanded: boolean;
  problems: Problem[];
  absoluteIndex?: number;
}

export type HybridProblem = ProblemsSet | Problem;

export interface ProblemsResponse {
  problemData: HybridProblem[];
  totalProblems: number;
}

/**
 * Problems cache state for storing fetched problems data
 * 
 * Handles:
 * - Current problems/problemsets display
 * - Shuffle state and order management
 * - Data transformations and validation
 */

export interface ProblemsState {
  problems_or_problemsset: HybridProblem[];
  isShuffled: boolean;
  shuffledOrder: number[];
}

export interface ProblemsActions {
  setProblems: (problems: HybridProblem[]) => void;
  setIsShuffled: (shuffled: boolean) => void;
  setShuffledOrder: (order: number[]) => void;
  shuffleQuestions: (sourceData?: HybridProblem[]) => void;
  clearProblems: () => void;
}

export type ProblemsStore = ProblemsState & ProblemsActions;

/**
 * Zustand store for problems data management
 * 
 * @example
 * ```ts
 * const { problems_or_problemsset, shuffleQuestions } = useProblemsStore();
 * ```
 */
export const useProblemsStore = create<ProblemsStore>((set, get) => ({
  // State
  problems_or_problemsset: [],
  isShuffled: false,
  shuffledOrder: [],

  // Actions
  setProblems: (problems) => set({ problems_or_problemsset: problems }),
  setIsShuffled: (shuffled) => set({ isShuffled: shuffled }),
  setShuffledOrder: (order) => set({ shuffledOrder: order }),
  clearProblems: () => set({ 
    problems_or_problemsset: [], 
    isShuffled: false, 
    shuffledOrder: [] 
  }),

  /**
   * Shuffles questions with solved questions placed at the end
   * 
   * @param sourceData - Optional source data to shuffle, uses store data if not provided
   */
  shuffleQuestions: (sourceData?: HybridProblem[]) => {
    const state = get();
    let problems: HybridProblem[] = [];
    
    if (sourceData && sourceData.length > 0) {
      problems = [...sourceData];
    } else if (state.problems_or_problemsset.length > 0) {
      problems = [...state.problems_or_problemsset];
    } else {
      return;
    }
    
    /**
     * Helper function to check if a problem/problemset is solved
     */
    const isSolved = (item: HybridProblem): boolean => {
      if ("isExpanded" in item) {
        // For ProblemsSet, check if ALL child problems are solved
        const problemSet = item as ProblemsSet;
        return problemSet.problems.every(problem => 
          problem.iscorrect !== null && problem.iscorrect !== undefined
        );
      } else {
        // For individual Problem
        const problem = item as Problem;
        return problem.iscorrect !== null && problem.iscorrect !== undefined;
      }
    };
    
    // Separate solved and unsolved items
    const solvedItems: HybridProblem[] = [];
    const unsolvedItems: HybridProblem[] = [];
    
    problems.forEach(item => {
      if (isSolved(item)) {
        solvedItems.push(item);
      } else {
        unsolvedItems.push(item);
      }
    });
    
    /**
     * Fisher-Yates shuffle algorithm
     */
    const shuffleArray = <T>(array: T[]): T[] => {
      const shuffled = [...array];
      for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
      }
      return shuffled;
    };
    
    const shuffledUnsolved = shuffleArray(unsolvedItems);
    const shuffledSolved = shuffleArray(solvedItems);
    
    // Combine: unsolved questions first, then solved questions
    const shuffledProblems = [...shuffledUnsolved, ...shuffledSolved];
    
    // Create shuffled order indices
    const shuffledOrder = shuffledProblems.map((_, index) => index);
    
    set({ 
      problems_or_problemsset: shuffledProblems,
      isShuffled: true,
      shuffledOrder: shuffledOrder,
    });
  },
}));