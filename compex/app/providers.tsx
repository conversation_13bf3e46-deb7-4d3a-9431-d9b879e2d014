/*
next-themes actually requires a ThemeProvider to wrap your application.
- responsible for managing the theme state and making it available via the useTheme hook.
- needed to create a client component to host the ThemeProvider
*/

"use client";

import * as React from "react";
import { ThemeProvider as NextThemesProvider } from "next-themes";
import { type ThemeProviderProps } from "next-themes";
import { initializeServiceWorker, setupOfflineDetection } from "@/shared/lib/cache/service-worker";
import { getQueryCacheService } from "@/shared/lib/cache/query-cache";

export default function ThemeProvider({
   children,
   ...props
}: ThemeProviderProps) {
   // Initialize caching systems on mount
   React.useEffect(() => {
      // Initialize service worker for offline caching
      initializeServiceWorker().then((status) => {
         if (status.isSupported) {
            console.log('✅ Service Worker initialized');
         } else {
            console.warn('⚠️ Service Worker not supported');
         }
      });

      // Setup offline detection
      setupOfflineDetection();

      // Preload common data
      const queryCacheService = getQueryCacheService();
      queryCacheService.preloadCommonData().catch((error) => {
         console.warn('⚠️ Failed to preload common data:', error);
      });
   }, []);

   return (
      <NextThemesProvider
         attribute="class"
         defaultTheme="system"
         enableSystem={true}
         disableTransitionOnChange={false}
         storageKey="compex-theme-preference"
         {...props}
      >
         {children}
      </NextThemesProvider>
   );
}
