import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import ReactQueryProvider from "@/shared/components/providers/react-query-provider";
import { WebVitalsTracker } from "@/shared/components/performance/WebVitalsTracker";
import { PerformanceMonitor, PerformanceBudgetChecker } from "@/shared/components/performance/PerformanceMonitor";
import "./globals.css";
import Providers from "./providers";
const inter = Inter({ subsets: ["latin"] });
export const metadata: Metadata = {
   title: "CompEx",
   description: "Platform to prepare for prepare for competitive exams",
};

export default function RootLayout({
   children,
}: Readonly<{
   children: React.ReactNode;
}>) {
   return (
      <html lang="en">
         <head>
            <script
               dangerouslySetInnerHTML={{
                  __html: `
                     (function() {
                        try {
                           var theme = localStorage.getItem('compex-theme-preference');
                           if (theme === 'dark' || (!theme && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
                              document.documentElement.classList.add('dark');
                           } else {
                              document.documentElement.classList.remove('dark');
                           }
                        } catch (e) {}
                     })();
                  `,
               }}
            />
         </head>
         <body className={inter.className}>
            <ReactQueryProvider>
               <Providers>
                  <WebVitalsTracker 
                     enableAnalytics={process.env.NODE_ENV === 'production'}
                     enableConsoleLog={process.env.NODE_ENV === 'development'}
                     endpoint="/api/metrics"
                     sampleRate={0.1}
                  />
                  {children}
                  <PerformanceMonitor />
                  <PerformanceBudgetChecker />
               </Providers>
            </ReactQueryProvider>
         </body>
      </html>
   );
}
