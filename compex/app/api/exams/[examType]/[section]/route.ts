import { NextRequest, NextResponse } from "next/server";
import { getProblemsForPagination } from "@/features/question-solving/services/question-queries";

/**
 * GET /api/exams/[examType]/[section] - Retrieve problems for specific exam type and section
 * 
 * Path Parameters:
 * - examType: string - Exam type (gre, gmat, cat)
 * - section: string - Section name (quants, verbal, etc.)
 * 
 * Query Parameters:
 * - userid: number - User ID for tracking attempts
 * - tags: string[] - Array of tags for filtering (JSON encoded)
 * - page: number - Page number for pagination
 * - pageSize: number - Number of items per page
 */
export async function GET(
   request: NextRequest,
   { params }: { params: { examType: string; section: string } }
) {
   const { examType, section } = params;
   const searchParams = request.nextUrl.searchParams;
   
   const userid = parseInt(searchParams.get("userid") || "1");
   const tagsParam = searchParams.get("tags");
   const tags = tagsParam ? JSON.parse(tagsParam) : [];
   const page = parseInt(searchParams.get("page") || "1");
   const pageSize = parseInt(searchParams.get("pageSize") || "10");

   // Normalize exam type to uppercase for consistency
   const examName = examType.toUpperCase();
   const sectionName = section.toLowerCase();

   // Validate exam type
   if (!["GRE", "GMAT", "CAT"].includes(examName)) {
      return NextResponse.json(
         {
            error: `Invalid exam type: ${examType}`,
            validExamTypes: ["gre", "gmat", "cat"]
         },
         { status: 400 }
      );
   }

   const examtypeid = ["GRE", "GMAT", "CAT"].indexOf(examName) + 1;
   let sectionid = -1;

   // Validate section for each exam type
   switch (examtypeid) {
      case 1: // GRE
         const greSections = ["quants", "verbal"];
         if (!greSections.includes(sectionName)) {
            return NextResponse.json(
               {
                  error: `Invalid section '${section}' for GRE`,
                  validSections: greSections
               },
               { status: 400 }
            );
         }
         sectionid = greSections.indexOf(sectionName) + 1;
         break;
      case 2: // GMAT
         const gmatSections = ["quants", "verbal", "integrated reasoning"];
         if (!gmatSections.includes(sectionName)) {
            return NextResponse.json(
               {
                  error: `Invalid section '${section}' for GMAT`,
                  validSections: gmatSections
               },
               { status: 400 }
            );
         }
         sectionid = gmatSections.indexOf(sectionName) + 3;
         break;
      case 3: // CAT
         const catSections = ["di", "lr", "va", "rc"];
         if (!catSections.includes(sectionName)) {
            return NextResponse.json(
               {
                  error: `Invalid section '${section}' for CAT`,
                  validSections: catSections
               },
               { status: 400 }
            );
         }
         sectionid = catSections.indexOf(sectionName) + 6;
         break;
   }

   try {
      if (tags?.length > 0) {
         tags.map((tag: string) => `"${tag}"`);
         console.log("Tags in :", tags);
      }

      const { problemData, totalProblems } = await getProblemsForPagination(
         userid,
         examtypeid,
         sectionid,
         tags
      );

      return NextResponse.json({
         problemData,
         totalProblems,
         pagination: {
            page,
            pageSize,
            totalPages: Math.ceil(totalProblems / pageSize)
         },
         metadata: {
            examType: examName,
            section: sectionName,
            examTypeId: examtypeid,
            sectionId: sectionid,
            tags,
            userId: userid
         }
      });
   } catch (error) {
      console.error(`Error fetching problems for ${examType}/${section}:`, error);
      return NextResponse.json(
         {
            error: `Error fetching problems for ${examType}/${section}`,
         },
         { status: 500 }
      );
   }
}