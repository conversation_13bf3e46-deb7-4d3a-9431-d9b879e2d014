import { NextRequest, NextResponse } from "next/server";
import { getProblemsForPagination } from "@/features/question-solving/services/question-queries";

/**
 * GET /api/problems - Retrieve problems with pagination and filtering
 *
 * Query Parameters:
 * - userid: number - User ID for tracking attempts
 * - examName: string - Exam type (GRE, GMAT, CAT)
 * - sectionName: string - Section name (varies by exam)
 * - tags: string[] - Array of tags for filtering
 * - page: number - Page number for pagination
 * - pageSize: number - Number of items per page
 */
export async function GET(request: NextRequest) {
   const searchParams = request.nextUrl.searchParams;
   const userid = parseInt(searchParams.get("userid") || "1");
   const examName = searchParams.get("examName") || "GRE";
   const sectionName = searchParams.get("sectionName") || "quants";
   const tagsParam = searchParams.get("tags");
   // Make sure tags is null when not provided, not an empty array
   const tags = tagsParam ? JSON.parse(tagsParam) : null;

   if (tags && tags.length > 0) {
      tags.map((tag: string) => `"${tag}"`);
      console.log("Tags in :", tags);
   }

   const examtypeid = ["GRE", "GMAT", "CAT"].indexOf(examName) + 1;
   let sectionid = -1;

   switch (examtypeid) {
      case 1:
         // GRE
         sectionid = ["quants", "verbal"].indexOf(sectionName) + 1;
         break;
      case 2:
         // GMAT
         sectionid =
            ["quants", "verbal", "integrated reasoning"].indexOf(sectionName) +
            3;
         break;
      case 3:
         // CAT
         sectionid = ["DI", "LR", "VA", "RC"].indexOf(sectionName) + 6;
         break;
      default:
         console.error(
            `Invalid exam type in api/problems/route.ts, examtypeid: ${examtypeid}`
         );
   }

   try {
      const { problemData, totalProblems } = await getProblemsForPagination(
         userid,
         examtypeid,
         sectionid,
         tags
      );

      return NextResponse.json({
         problemData,
         totalProblems,
         pagination: {
            examName,
            sectionName,
            tags,
         },
      });
   } catch (error) {
      console.error(`Error fetching problems:`, error);
      return NextResponse.json(
         {
            error: "Error fetching problems, probably Docker for DB is not running!",
         },
         { status: 500 }
      );
   }
}

/**
 * POST /api/problems - Legacy endpoint for backward compatibility
 * @deprecated Use GET /api/problems with query parameters instead
 */
export async function POST(req: NextRequest) {
   const { userid, examName, sectionName, tags } = await req.json();

   // Redirect to GET method with query parameters
   const searchParams = new URLSearchParams({
      userid: userid.toString(),
      examName: examName || "GRE",
      sectionName: sectionName || "quants",
      tags: JSON.stringify(tags || []),
   });

   const url = new URL(`/api/problems?${searchParams}`, req.url);
   const getRequest = new NextRequest(url, { method: "GET" });

   return GET(getRequest);
}
