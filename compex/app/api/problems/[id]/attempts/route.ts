import { NextRequest, NextResponse } from "next/server";
import { submitUserAttempt } from "@/features/question-solving/services/question-queries";

/**
 * POST /api/problems/[id]/attempts - Submit user attempt for a specific problem
 * 
 * Path Parameters:
 * - id: string - Problem ID
 * 
 * Request Body:
 * {
 *   userID: number,
 *   Attempt: [{
 *     questionID: number,
 *     option: string
 *   }]
 * }
 */
export async function POST(
   req: NextRequest,
   { params }: { params: { id: string } }
) {
   try {
      const { id } = params;
      const body = await req.json();
      
      // Validate that the attempt is for the correct problem
      const problemId = parseInt(id);
      if (body.Attempt && body.Attempt.length > 0) {
         const firstAttempt = body.Attempt[0];
         if (firstAttempt.questionID !== problemId) {
            return NextResponse.json(
               { 
                  error: "Problem ID in URL does not match attempt data",
                  expected: problemId,
                  received: firstAttempt.questionID
               },
               { status: 400 }
            );
         }
      }

      const sentDetails = await submitUserAttempt(body);
      
      return NextResponse.json({
         success: true,
         data: sentDetails,
         metadata: {
            problemId,
            attemptCount: body.Attempt?.length || 0,
            userId: body.userID
         }
      });
   } catch (error) {
      console.error(`Error submitting user attempt for problem ${params.id}:`, error);
      return NextResponse.json(
         {
            error: `Error submitting user attempt for problem ${params.id}`,
         },
         { status: 500 }
      );
   }
}

/**
 * GET /api/problems/[id]/attempts - Retrieve user attempts for a specific problem
 * 
 * Path Parameters:
 * - id: string - Problem ID
 * 
 * Query Parameters:
 * - userId: number - User ID to filter attempts
 */
export async function GET(
   request: NextRequest,
   { params }: { params: { id: string } }
) {
   try {
      const { id } = params;
      const searchParams = request.nextUrl.searchParams;
      const userId = searchParams.get("userId");

      if (!userId) {
         return NextResponse.json(
            { error: "userId query parameter is required" },
            { status: 400 }
         );
      }

      // TODO: Implement getUserAttemptsForProblem service
      // const attempts = await getUserAttemptsForProblem(parseInt(id), parseInt(userId));

      return NextResponse.json({
         problemId: parseInt(id),
         userId: parseInt(userId),
         attempts: [], // Placeholder - implement actual service
         message: "GET attempts endpoint - implementation pending"
      });
   } catch (error) {
      console.error(`Error fetching attempts for problem ${params.id}:`, error);
      return NextResponse.json(
         { error: `Error fetching attempts for problem ${params.id}` },
         { status: 500 }
      );
   }
}