import { NextRequest, NextResponse } from "next/server";
import { getTags } from "@/features/exam-management/services/tag-queries";

/**
 * GET /api/problems/tags - Retrieve available tags for filtering
 * 
 * Query Parameters:
 * - examName: string - Exam type (GRE, GMAT, CAT)
 * - sectionName: string - Section name (varies by exam type)
 */
export async function GET(request: NextRequest) {
   const searchParams = request.nextUrl.searchParams;
   const examName = searchParams.get("examName") || "GRE";
   const sectionName = searchParams.get("sectionName") || "quants";

   const examtypeid: number = ["GRE", "GMAT", "CAT"].indexOf(examName) + 1;
   let sectionid = -1;

   switch (examtypeid) {
      case 1:
         // GRE
         sectionid = ["quants", "verbal"].indexOf(sectionName) + 1;
         break;
      case 2:
         // GMAT
         sectionid = ["quants", "verbal", "integrated reasoning"].indexOf(sectionName) + 3;
         break;
      case 3:
         // CAT
         sectionid = ["DI", "LR", "VA", "RC"].indexOf(sectionName) + 6;
         break;
      default:
         console.error(`Invalid exam type in api/problems/tags/route.ts, examtypeid: ${examtypeid}`);
   }

   // Validate exam and section combination
   const isValidCombination = 
      (examtypeid === 1 && sectionid >= 1 && sectionid <= 2) ||
      (examtypeid === 2 && sectionid >= 3 && sectionid <= 5) ||
      (examtypeid === 3 && sectionid >= 6 && sectionid <= 9);

   if (!isValidCombination) {
      return NextResponse.json(
         {
            error: `Invalid combination: ${examName} (${examtypeid}) and ${sectionName} (${sectionid})`,
            availableCombinations: {
               GRE: ["quants", "verbal"],
               GMAT: ["quants", "verbal", "integrated reasoning"],
               CAT: ["DI", "LR", "VA", "RC"]
            }
         },
         { status: 400 }
      );
   }

   try {
      const tagData = await getTags(examtypeid, sectionid);
      
      return NextResponse.json({
         tags: tagData,
         metadata: {
            examName,
            sectionName,
            examTypeId: examtypeid,
            sectionId: sectionid,
            count: tagData?.length || 0
         }
      });
   } catch (error) {
      console.error(`Error fetching tags: ${error}\t\tapi/problems/tags/route.ts`);
      return NextResponse.json(
         {
            error: "Error fetching tags, probably Docker for DB is not running!",
         },
         { status: 500 }
      );
   }
}

/**
 * POST /api/problems/tags - Legacy endpoint for backward compatibility
 * @deprecated Use GET /api/problems/tags with query parameters instead
 */
export async function POST(req: NextRequest) {
   const { examName, sectionName } = await req.json();
   
   // Redirect to GET method with query parameters
   const searchParams = new URLSearchParams({
      examName: examName || "GRE",
      sectionName: sectionName || "quants"
   });

   const url = new URL(`/api/problems/tags?${searchParams}`, req.url);
   const getRequest = new NextRequest(url, { method: "GET" });
   
   return GET(getRequest);
}