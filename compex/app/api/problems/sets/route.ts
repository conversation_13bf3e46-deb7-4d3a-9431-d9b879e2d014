import { NextRequest, NextResponse } from "next/server";
import { getProblemsSetForPagination } from "@/features/question-solving/services/problemset-queries";

/**
 * GET /api/problems/sets - Retrieve problem sets with pagination and filtering
 * 
 * Query Parameters:
 * - userid: number - User ID for tracking attempts
 * - examName: string - Exam type (GRE, GMAT, CAT)
 * - sectionName: string - Section name (varies by exam)
 * - tags: string[] - Array of tags for filtering (JSON encoded)
 * - page: number - Page number for pagination
 * - pageSize: number - Number of items per page
 */
export async function GET(request: NextRequest) {
   const searchParams = request.nextUrl.searchParams;
   const userid = parseInt(searchParams.get("userid") || "1");
   const examName = searchParams.get("examName") || "GRE";
   const sectionName = searchParams.get("sectionName") || "quants";
   const tagsParam = searchParams.get("tags");
   const tags = tagsParam ? JSON.parse(tagsParam) : [];
   const page = parseInt(searchParams.get("page") || "1");
   const pageSize = parseInt(searchParams.get("pageSize") || "10");

   if (!examName || !sectionName) {
      return NextResponse.json(
         { error: "Missing examName or sectionName query parameters" },
         { status: 400 }
      );
   }

   const examtypeid = ["GRE", "GMAT", "CAT"].indexOf(examName) + 1;

   if (examtypeid === 0) {
      return NextResponse.json(
         { 
            error: "Invalid examName provided",
            validExamTypes: ["GRE", "GMAT", "CAT"]
         },
         { status: 400 }
      );
   }

   let sectionid = -1;
   switch (examtypeid) {
      case 1: // GRE
         const greSections = ["quants", "verbal"];
         sectionid = greSections.indexOf(sectionName) + 1;
         if (sectionid === 0) {
            return NextResponse.json(
               { 
                  error: `Invalid sectionName for GRE: ${sectionName}`,
                  validSections: greSections
               },
               { status: 400 }
            );
         }
         break;
      case 2: // GMAT
         const gmatSections = ["quants", "verbal", "integrated reasoning"];
         sectionid = gmatSections.indexOf(sectionName) + 3;
         if (sectionid === 2) { // indexOf returns -1, so +3 = 2
            return NextResponse.json(
               { 
                  error: `Invalid sectionName for GMAT: ${sectionName}`,
                  validSections: gmatSections
               },
               { status: 400 }
            );
         }
         break;
      case 3: // CAT
         const catSections = ["DI", "LR", "VA", "RC"];
         sectionid = catSections.indexOf(sectionName) + 6;
         if (sectionid === 5) { // indexOf returns -1, so +6 = 5
            return NextResponse.json(
               { 
                  error: `Invalid sectionName for CAT: ${sectionName}`,
                  validSections: catSections
               },
               { status: 400 }
            );
         }
         break;
      default:
         return NextResponse.json(
            { error: "Invalid exam type" },
            { status: 400 }
         );
   }

   try {
      const { problemSetWithProblems, totalProblemSets } =
         await getProblemsSetForPagination(userid, examtypeid, sectionid, tags);

      return NextResponse.json({
         problemSetWithProblems,
         totalProblemSets,
         pagination: {
            page,
            pageSize,
            totalPages: Math.ceil(totalProblemSets / pageSize)
         },
         metadata: {
            examName,
            sectionName,
            examTypeId: examtypeid,
            sectionId: sectionid,
            tags,
            userId: userid
         }
      });
   } catch (error: any) {
      console.error("Error in problem sets route:", error);
      return NextResponse.json(
         { error: "Internal Server Error fetching problem sets" },
         { status: 500 }
      );
   }
}

/**
 * POST /api/problems/sets - Legacy endpoint for backward compatibility
 * @deprecated Use GET /api/problems/sets with query parameters instead
 */
export async function POST(req: NextRequest) {
   const { userid, examName, sectionName, tags } = await req.json();
   
   // Redirect to GET method with query parameters
   const searchParams = new URLSearchParams({
      userid: userid?.toString() || "1",
      examName: examName || "GRE",
      sectionName: sectionName || "quants",
      tags: JSON.stringify(tags || [])
   });

   const url = new URL(`/api/problems/sets?${searchParams}`, req.url);
   const getRequest = new NextRequest(url, { method: "GET" });
   
   return GET(getRequest);
}