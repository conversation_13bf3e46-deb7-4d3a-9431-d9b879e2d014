import { NextRequest, NextResponse } from "next/server";
import { query } from "@/shared/lib/configs/database";

export async function GET(req: NextRequest) {
   try {
      const result = await query(`
         SELECT tablename
         FROM pg_catalog.pg_tables
         WHERE schemaname != 'pg_catalog' AND schemaname != 'information_schema';`);
      return NextResponse.json(result.rows);
   } catch (err) {
      console.error("Failed to fetch users:", err);
      return NextResponse.json(
         { message: "Failed to fetch users" },
         { status: 500 }
      );
   }
}
