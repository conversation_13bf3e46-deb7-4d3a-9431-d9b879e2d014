// pages/index.tsx

import Image from "next/image";
import React from "react";
import { ButtonP, ButtonS } from "@/shared/components/ui/button";
import ThemeToggle from "@/shared/components/ui/theme-toggle";
import Header from "@/shared/components/layouts/header";
import Footer from "@/shared/components/layouts/footer";
import { Pfont, Sfont } from "@/shared/lib/configs/fonts";
import { Hflow, Vflow } from "@/shared/components/layouts/flows";
import Logo from "@/public/images/Logo.png";

const content = {
   left: {
      heading: "Quick Link",
      children: {
         Home: "#",
         Features: "#",
         Exams: "#",
         Testimonials: "#",
      },
   },
   right: {
      heading: "Exams",
      children: {
         GRE: "#",
         GMAT: "#",
         SAT: "#",
         CAT: "#",
      },
   },
};

const Home: React.FC = () => {
   const footerItems = Object.entries(content).map(
      ([direction, obj], index) => {
         return (
            <Vflow gap={4} key={index}>
               <h4 className="text-lg font-bold mb-4">{obj.heading}</h4>
               <ul className="space-y-2">
                  {Object.entries(obj.children).map(([name, href], index) => {
                     return (
                        <li key={index}>
                           <a href={href} className="hover:underline">
                              {name}
                           </a>
                        </li>
                     );
                  })}
               </ul>
            </Vflow>
         );
      }
   );
   return (
      <div className="flex flex-col min-h-screen">
         <Header
            leftItems={[
               <h1 className="text-3xl font-bold" key={0}>
                  CompEx
               </h1>,
            ]}
            centerItems={[
               <a href="#" className="hover:underline" key={1}>
                  Features
               </a>,
               <a href="/dashboard/mock" className="hover:underline" key={2}>
                  Exams
               </a>,
               <a href="#" className="hover:underline" key={3}>
                  Testimonials
               </a>,
               <a href="#" className="hover:underline" key={4}>
                  Contact
               </a>,
            ]}
            rightItems={[
               <ButtonP key={1}>Login</ButtonP>,
               <ThemeToggle key={2} />,
            ]}
         />
         <main className="flex-1">
            <section className="bg-primary py-20 md:py-24 lg:py-32 text-center text-primary-foreground">
               <div className="container mx-auto px-6 md:px-8 lg:px-10">
                  <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-4">
                     Ace Your Exams with Our Practice Tests
                  </h1>
                  <p className="text-lg md:text-xl lg:text-2xl mb-8">
                     Prepare for GRE, GMAT, and CAT with our comprehensive
                     practice tests and category-wise questions.
                  </p>
                  <ButtonP>Start Mock Test</ButtonP>
               </div>
            </section>
            <section
               id="features"
               className="bg-background py-12 md:py-16 lg:py-20"
            >
               <div className="container mx-auto px-6 md:px-8 lg:px-10">
                  <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-8">
                     Key Features
                  </h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                     <div className="bg-card p-6 rounded-lg shadow-md">
                        <Pfont style={1} className="text-xl font-bold mb-2">
                           Category-wise Questions
                        </Pfont>
                        <p className="text-muted-foreground">
                           Access a wide range of category-specific questions to
                           target your weaknesses.
                        </p>
                     </div>
                     <div className="bg-card p-6 rounded-lg shadow-md">
                        <Pfont style={1} className="text-xl font-bold mb-2">
                           Topic-wise Analysis
                        </Pfont>
                        <p className="text-muted-foreground">
                           Receive detailed analysis of your performance to
                           identify and improve your weak areas.
                        </p>
                     </div>
                     <div className="bg-card p-6 rounded-lg shadow-md">
                        <Pfont style={1} className="text-xl font-bold mb-2">
                           Practice Tests
                        </Pfont>
                        <p className="text-muted-foreground">
                           Take full-length mock tests to simulate the actual
                           exam experience.
                        </p>
                     </div>
                  </div>
               </div>
            </section>
            <section
               id="exams"
               className="bg-background py-12 md:py-16 lg:py-20"
            >
               <div className="container mx-auto px-6 md:px-8 lg:px-10">
                  <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-8">
                     Exam Categories
                  </h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                     <div className="bg-card p-6 rounded-lg shadow-md">
                        <Sfont style={1} className="text-xl font-bold mb-2">
                           GRE
                        </Sfont>
                        <p className="text-muted-foreground mb-4">
                           Prepare for the Graduate Record Examination with our
                           comprehensive practice tests and questions.
                        </p>
                        <a href="#" className="text-primary hover:underline">
                           Explore GRE
                        </a>
                     </div>
                     <div className="bg-card p-6 rounded-lg shadow-md">
                        <Sfont style={1} className="text-xl font-bold mb-2">
                           GMAT
                        </Sfont>
                        <p className="text-muted-foreground mb-4">
                           Get ready for the Graduate Management Admission Test
                           with our practice materials.
                        </p>
                        <a href="#" className="text-primary hover:underline">
                           Explore GMAT
                        </a>
                     </div>
                     <div className="bg-card p-6 rounded-lg shadow-md">
                        <Sfont style={1} className="text-xl font-bold mb-2">
                           CAT
                        </Sfont>
                        <p className="text-muted-foreground mb-4">
                           Prepare for the Common Admission Test with our
                           comprehensive practice resources.
                        </p>
                        <a href="#" className="text-primary hover:underline">
                           Explore CAT
                        </a>
                     </div>
                  </div>
               </div>
            </section>
            <section
               id="testimonials"
               className="bg-background py-12 md:py-16 lg:py-20"
            >
               <div className="container mx-auto px-6 md:px-8 lg:px-10">
                  <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-8">
                     What Our Users Say
                  </h2>
                  <div className="bg-card p-6 rounded-lg shadow-md">
                     <div className="flex items-center mb-4">
                        <div className="w-10 h-10 mr-4 rounded-full bg-gray-300"></div>
                        <div>
                           <h4 className="text-lg font-bold">John Doe</h4>
                           <p className="text-muted-foreground">
                              Satisfied User
                           </p>
                        </div>
                     </div>
                     <p className="text-muted-foreground">
                        &quot;I&apos;ve been using this website for my exam
                        preparation, and it&apos;s been a game-changer. The
                        practice tests and category-wise questions have really
                        helped me identify and improve my weak areas.&quot;
                     </p>
                  </div>
               </div>
            </section>
         </main>
         <Footer
            className="bg-primary text-primary-foreground py-8 md:py-12"
            centerItems={footerItems}
         />
      </div>
   );
};

export default Home;
