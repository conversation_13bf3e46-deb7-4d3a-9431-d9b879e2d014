"use client";
import React from "react";
import Header from "@/shared/components/layouts/header";
import { ButtonLinks } from "@/shared/components/ui/button";
import { Pfont } from "@/shared/lib/configs/fonts";
import Image from "next/image";
import userUndefined from "@/public/images/userUndefined.svg";
import MinimalDropdown from "@/shared/components/ui/dropdown";
import { useProblemForPaginationStore } from "@/features/question-solving/hooks/(pagination)/problemDefinition";
import ThemeToggle from "@/shared/components/ui/theme-toggle";
import { usePathname } from "next/navigation";

const headerComponents = {
   left: {
      properties: "font-medium ",
      elements: {
         problems: {
            bgColor: "hsl(var(--secondary-light))",
            bgColorInactive: "hsl(var(--background))",
            textColor: "bg-white dark:bg-gray-200",
            textColorInactive: "bg-bg-gray-200 dark:bg-white",
            href: "/dashboard/problems",
         },
         mock: {
            bgColor: "hsl(var(--secondary-light))",
            bgColorInactive: "hsl(var(--background))",
            textColor: "bg-white dark:bg-gray-200",
            textColorInactive: "bg-bg-gray-200 dark:bg-white",
            href: "/dashboard/mock",
         },
         "dev-notes": {
            bgColor: "hsl(var(--secondary-light))",
            bgColorInactive: "hsl(var(--background))",
            textColor: "bg-white dark:bg-gray-200",
            textColorInactive: "bg-bg-gray-200 dark:bg-white",
            href: "/dashboard/devnotes",
         },
      },
   },
   center: {
      properties: "tailwind properties",
      logo: "CompEx",
   },
   right: {
      properties: "h-10 w-10",
      userImg: userUndefined,
   },
};
export default function Layout({ children }: { children: React.ReactNode }) {
   const { examName, setExamName } = useProblemForPaginationStore();
   const pathname = usePathname();

   const hLeft = Object.entries(headerComponents.left.elements).map(
      ([name, content], index) => {
         const isActive = pathname.includes(content.href);
         return (
            <ButtonLinks
               className={headerComponents.left.properties}
               name={name}
               isActive={isActive}
               href={content.href}
               color1={content.bgColor}
               color2={content.bgColorInactive}
               textColor={content.textColor}
               textColorInactive={content.textColorInactive}
               key={index}
            />
         );
      }
   );
   const hcenter = (
      <Pfont style={1} className="text-2xl font-bold">
         {headerComponents.center.logo}
      </Pfont>
   );
   const hright = (
      <Pfont style={1}>
         <div className="flex gap-5 items-center">
            <MinimalDropdown
               items={["GRE", "GMAT", "CAT"]}
               getStoreName={() => examName}
               setStoreName={(name: string) => setExamName(name)}
               defaultVal="Select an Exam"
            />
            <ThemeToggle />
            <div className="bg-white dark:bg-gray-200 p-1 rounded-full border border-border">
               <Image
                  src={headerComponents.right.userImg}
                  className={headerComponents.right.properties}
                  alt="user logo"
               />
            </div>
         </div>
      </Pfont>
   );
   return (
      <>
         <Header
            leftItems={hLeft}
            centerItems={[hcenter]}
            rightItems={[hright]}
         />
         {children}
      </>
   );
}
