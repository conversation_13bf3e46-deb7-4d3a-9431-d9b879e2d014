"use client";
import { useState } from "react";

export interface ProblemsPageState {
   displaySolvedQuestions: boolean;
   isQuestionWindowOpen: boolean;
   isResultWindowOpen: boolean;
   showTimer: boolean;
   showDifficulty: boolean;
   shuffledData: any;
   isShuffled: boolean;
}

export function useProblemsPageState() {
   const [displaySolvedQuestions, setDisplaySolvedQuestion] =
      useState<boolean>(false);
   const [isQuestionWindowOpen, setIsQuestionWindowOpen] =
      useState<boolean>(false);
   const [isResultWindowOpen, setIsResultWindowOpen] = useState<boolean>(false);
   const [showTimer, setShowTimer] = useState<boolean>(true);
   const [showDifficulty, setShowDifficulty] = useState<boolean>(true);
   const [shuffledData, setShuffledData] = useState<any>(null);
   const [isShuffled, setIsShuffled] = useState<boolean>(false);

   return {
      // State values
      displaySolvedQuestions,
      isQuestionWindowOpen,
      isResultWindowOpen,
      showTimer,
      showDifficulty,
      shuffledData,
      isShuffled,

      // State setters
      setDisplaySolvedQuestion,
      setIsQuestionWindowOpen,
      setIsResultWindowOpen,
      setShowTimer,
      setShowDifficulty,
      setShuffledData,
      setIsShuffled,
   };
}
