"use client";
import React from "react";
import { useAttemptStore } from "@/features/question-solving/hooks/(pagination)/problemDefinition";
import { questionList } from "@/features/question-solving/hooks/(pagination)/problemDefinition";

export function useQuestionWindowControls() {
  const { Attempt } = useAttemptStore();
  const { setStart } = questionList();

  const handleCloseQuestionWindow = React.useCallback(
    (
      setIsQuestionWindowOpen: React.Dispatch<React.SetStateAction<boolean>>,
      setIsResultWindowOpen: React.Dispatch<React.SetStateAction<boolean>>
    ) => {
      setIsQuestionWindowOpen(false);
      setStart(-1);
      // Only open result window if there are valid attempts
      const hasValidAttempts = Attempt?.some(
        (attempt) => attempt.option.length > 0
      );
      if (hasValidAttempts) {
        setIsResultWindowOpen(true);
      }
    },
    [Attempt, setStart]
  );

  const handleOpenQuestionWindow = React.useCallback(
    (setIsQuestionWindowOpen: React.Dispatch<React.SetStateAction<boolean>>) => {
      setIsQuestionWindowOpen(true);
    },
    []
  );

  const handleCloseResultWindow = React.useCallback(
    (setIsResultWindowOpen: React.Dispatch<React.SetStateAction<boolean>>) => {
      setStart(-1);
      setIsResultWindowOpen(false);
    },
    [setStart]
  );

  return {
    handleCloseQuestionWindow,
    handleOpenQuestionWindow,
    handleCloseResultWindow,
    hasValidAttempts: Attempt?.some((attempt) => attempt.option.length > 0),
  };
}