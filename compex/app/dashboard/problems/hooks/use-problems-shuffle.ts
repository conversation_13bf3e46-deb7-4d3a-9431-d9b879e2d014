"use client";
import React from "react";
import { useProblemForPaginationStore } from "@/features/question-solving/hooks/(pagination)/problemDefinition";

export function useProblemsShuffleLogic() {
  const { shuffleQuestions } = useProblemForPaginationStore();

  const handleShuffle = React.useCallback(
    (
      problemData: any,
      setShuffledData: React.Dispatch<React.SetStateAction<any>>,
      setIsShuffled: React.Dispatch<React.SetStateAction<boolean>>
    ) => {
      console.log("Handle shuffle called");
      if (!problemData.fullData || problemData.fullData.length === 0) {
        console.log("No data available to shuffle");
        return;
      }

      const data = [...problemData.fullData];
      console.log("Shuffling", data.length, "items");

      // Helper function to check if a problem/problemset is solved
      const isSolved = (item: any): boolean => {
        if ("isExpanded" in item) {
          // For ProblemsSet, check if ALL child problems are solved
          return item.problems.every(
            (problem: any) =>
              problem.iscorrect !== null && problem.iscorrect !== undefined
          );
        } else {
          // For individual Problem
          return item.iscorrect !== null && item.iscorrect !== undefined;
        }
      };

      // Separate solved and unsolved items
      const solvedItems: any[] = [];
      const unsolvedItems: any[] = [];

      data.forEach((item) => {
        if (isSolved(item)) {
          solvedItems.push(item);
        } else {
          unsolvedItems.push(item);
        }
      });

      console.log(
        "Unsolved items:",
        unsolvedItems.length,
        "Solved items:",
        solvedItems.length
      );

      // Shuffle both arrays using Fisher-Yates algorithm
      const shuffleArray = (array: any[]): any[] => {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
          const j = Math.floor(Math.random() * (i + 1));
          [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
      };

      const shuffledUnsolved = shuffleArray(unsolvedItems);
      const shuffledSolved = shuffleArray(solvedItems);

      // Combine: unsolved questions first, then solved questions
      const shuffledProblems = [...shuffledUnsolved, ...shuffledSolved];

      console.log(
        "Setting shuffled data with",
        shuffledProblems.length,
        "items"
      );

      setShuffledData({
        problemData: shuffledProblems,
        totalProblems: shuffledProblems.length,
      });
      setIsShuffled(true);

      // Update the questionIds in the store to maintain navigation order
      shuffleQuestions(shuffledProblems);
    },
    [shuffleQuestions]
  );

  return {
    handleShuffle,
  };
}