"use client";

import {
   <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
   LazyResultWindow,
} from "@/shared/components/feedback/LazyComponents";
import QuestionAttempt from "@/features/question-solving/components/QuestionWindow/questionwindow";
import { usePaginatedProblems } from "@/features/question-solving/hooks/(pagination)/fetchProblems";
import { useGetTags } from "@/features/question-solving/hooks/(pagination)/fetchTags";
import { useAttemptStore } from "@/features/question-solving/hooks/(pagination)/problemDefinition";
import { useProblemsPageState } from "./hooks/use-problems-page-state";
import { useQuestionWindowControls } from "./hooks/use-question-window-controls";
import { useProblemsShuffleLogic } from "./hooks/use-problems-shuffle";
import { ProblemsPanelLayout } from "./components/problems-panel-layout";

/**
 * Main problems page component - Clean, focused component using extracted hooks
 *
 * @returns JSX.Element - The problems page layout with question window and result window
 */
export default function ProblemsPage() {
   const problemData = usePaginatedProblems();
   const tagData = useGetTags();
   const { bookmarksEnabled, setBookmarksEnabled } = useAttemptStore();

   const pageState = useProblemsPageState();
   const windowControls = useQuestionWindowControls();
   const shuffleLogic = useProblemsShuffleLogic();

   const processedProblemData =
      pageState.isShuffled && pageState.shuffledData
         ? { ...problemData, data: pageState.shuffledData }
         : problemData;

   return (
      <div className="p-4 w-full">
         {pageState.isResultWindowOpen && windowControls.hasValidAttempts && (
            <LazyResultWindow
               onClose={() =>
                  windowControls.handleCloseResultWindow(
                     pageState.setIsResultWindowOpen
                  )
               }
            />
         )}

         {pageState.isQuestionWindowOpen && (
            <QuestionAttempt
               onClose={() =>
                  windowControls.handleCloseQuestionWindow(
                     pageState.setIsQuestionWindowOpen,
                     pageState.setIsResultWindowOpen
                  )
               }
               showTimer={pageState.showTimer}
            />
         )}

         <ProblemsPanelLayout
            tagData={tagData}
            problemData={processedProblemData}
            displaySolvedQuestions={pageState.displaySolvedQuestions}
            setDisplaySolvedQuestions={pageState.setDisplaySolvedQuestion}
            bookmarksEnabled={bookmarksEnabled}
            setBookmarksEnabled={setBookmarksEnabled}
            showTimer={pageState.showTimer}
            setShowTimer={pageState.setShowTimer}
            showDifficulty={pageState.showDifficulty}
            setShowDifficulty={pageState.setShowDifficulty}
            openQuestion={() =>
               windowControls.handleOpenQuestionWindow(
                  pageState.setIsQuestionWindowOpen
               )
            }
            calendar={<LazyCalendar />}
            onShuffle={() =>
               shuffleLogic.handleShuffle(
                  problemData,
                  pageState.setShuffledData,
                  pageState.setIsShuffled
               )
            }
         />
      </div>
   );
}
