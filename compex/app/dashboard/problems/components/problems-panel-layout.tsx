"use client";
import React from "react";
import { Hflow } from "@/shared/components/layouts/flows";
import { Tag } from "@/features/question-solving/hooks/(definitions)/tagDefinition";
import { ProblemCategoriesSection } from "./problem-categories-section";
import { ProblemsTableSection } from "./problems-table-section";
import { PreferenceSettingsPanel } from "./preference-settings-panel";
import { ExamSectionPanel } from "@/features/exam-management/components/exam-section-panel";

interface ProblemsPanelLayoutProps {
  tagData: {
    tagStatus: string;
    tagError: unknown;
    tagData: Tag[] | undefined;
  };
  problemData: {
    status: string;
    error: unknown;
    data: any;
    previousData: any;
    fullData?: any;
  };
  displaySolvedQuestions: boolean;
  setDisplaySolvedQuestions: React.Dispatch<React.SetStateAction<boolean>>;
  bookmarksEnabled: boolean;
  setBookmarksEnabled: (enabled: boolean) => void;
  showTimer: boolean;
  setShowTimer: React.Dispatch<React.SetStateAction<boolean>>;
  showDifficulty: boolean;
  setShowDifficulty: React.Dispatch<React.SetStateAction<boolean>>;
  openQuestion: () => void;
  calendar: React.ReactNode;
  onShuffle: () => void;
}

export const ProblemsPanelLayout = React.memo(function ProblemsPanelLayout({
  tagData,
  problemData,
  displaySolvedQuestions,
  setDisplaySolvedQuestions,
  bookmarksEnabled,
  setBookmarksEnabled,
  showTimer,
  setShowTimer,
  showDifficulty,
  setShowDifficulty,
  openQuestion,
  calendar,
  onShuffle,
}: ProblemsPanelLayoutProps) {
  return (
    <Hflow gap={5}>
      <ExamSectionPanel
        tagData={tagData}
        problemData={problemData}
        displaySolvedQuestions={displaySolvedQuestions}
        setDisplaySolvedQuestions={setDisplaySolvedQuestions}
        showDifficulty={showDifficulty}
        openQuestion={openQuestion}
        problemCategoriesComponent={ProblemCategoriesSection}
        problemsTableComponent={ProblemsTableSection}
      />

      <div className="mx-10">
        <div className="mb-5">{calendar}</div>
        
        <PreferenceSettingsPanel
          bookmarksEnabled={bookmarksEnabled}
          setBookmarksEnabled={setBookmarksEnabled}
          showTimer={showTimer}
          setShowTimer={setShowTimer}
          showDifficulty={showDifficulty}
          setShowDifficulty={setShowDifficulty}
          onShuffle={onShuffle}
        />
      </div>
    </Hflow>
  );
});