import React from "react";
import QuestionTable from "@/features/question-solving/components/question-table/question-table";
import SmartDataLoader from "@/shared/components/feedback/SmartDataLoader";
import { QuestionTableSkeleton } from "@/shared/components/feedback/SkeletonLoaders";
import { useProblemForPaginationStore } from "@/features/question-solving/hooks/(pagination)/problemDefinition";
import uiStrings from "../config/ui-strings.json";

interface ProblemsTableSectionProps {
   problemData: {
      status: string;
      error: unknown;
      data: any;
      previousData: any;
      fullData?: any;
   };
   displaySolvedQuestions: boolean;
   showDifficulty: boolean;
   openQuestion: () => void;
}

export function ProblemsTableSection({
   problemData,
   displaySolvedQuestions,
   showDifficulty,
   openQuestion,
}: ProblemsTableSectionProps) {
   const { sectionName } = useProblemForPaginationStore();

   return (
      <div>
         <h1 className="text-2xl tracking-tight mb-4 mt-6">
            {uiStrings.headers.problems}
         </h1>

         <SmartDataLoader
            status={problemData.status as any}
            data={problemData}
            error={problemData.error}
            loader={<QuestionTableSkeleton />}
            loadingMessage={uiStrings.messages.loadingProblems}
            className="min-h-[400px]"
            suppressLoadingWhenDataExists={true}
            dataKey={`problems-${sectionName}-${
               problemData.status
            }-${JSON.stringify(problemData.data?.totalProblems || 0)}`}
         >
            {(data) => (
               <QuestionTable
                  queryData={data}
                  openQuestion={openQuestion}
                  displaySolvedQuestions={displaySolvedQuestions}
                  showDifficulty={showDifficulty}
               />
            )}
         </SmartDataLoader>
      </div>
   );
}
