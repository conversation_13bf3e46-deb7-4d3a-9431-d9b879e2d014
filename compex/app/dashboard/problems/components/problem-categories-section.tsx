"use client";
import React from "react";
import Section from "@/features/exam-management/components/tag-properties";
import SmartDataLoader from "@/shared/components/feedback/SmartDataLoader";
import { TagSectionSkeleton } from "@/shared/components/feedback/SkeletonLoaders";
import { Tag } from "@/features/question-solving/hooks/(definitions)/tagDefinition";
import { useProblemForPaginationStore } from "@/features/question-solving/hooks/(pagination)/problemDefinition";
import uiStrings from "../config/ui-strings.json";

interface ProblemCategoriesSectionProps {
  tagData: {
    tagStatus: string;
    tagError: unknown;
    tagData: Tag[] | undefined;
  };
  displaySolvedQuestions: boolean;
  setDisplaySolvedQuestions: React.Dispatch<React.SetStateAction<boolean>>;
}

export function ProblemCategoriesSection({
  tagData,
  displaySolvedQuestions,
  setDisplaySolvedQuestions,
}: ProblemCategoriesSectionProps) {
  const { sectionName } = useProblemForPaginationStore();

  return (
    <div className="flex flex-col gap-[5px]">
      <h1 className="text-2xl tracking-tight mb-4">
        {uiStrings.headers.problemCategories}
      </h1>
      
      <SmartDataLoader
        status={tagData.tagStatus as any}
        data={tagData.tagData}
        error={tagData.tagError}
        loader={<TagSectionSkeleton />}
        loadingMessage={uiStrings.messages.loadingCategories}
        emptyComponent={
          <div className="text-center py-8 text-muted-foreground">
            {uiStrings.messages.noCategoriesAvailable}
          </div>
        }
        className="min-h-[120px]"
        suppressLoadingWhenDataExists={true}
        dataKey={`tags-${sectionName}-${tagData.tagStatus}`}
      >
        {(data) => (
          <Section
            tagData={data}
            displaySolvedQuestions={displaySolvedQuestions}
            setDisplaySolvedQuestions={setDisplaySolvedQuestions}
          />
        )}
      </SmartDataLoader>
    </div>
  );
}