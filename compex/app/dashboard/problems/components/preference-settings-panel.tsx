import React from "react";
import { Switch } from "@/shared/components/ui/switch";
import { Shuffle } from "lucide-react";
import uiStrings from "../config/ui-strings.json";

interface PreferenceSettingsPanelProps {
   bookmarksEnabled: boolean;
   setBookmarksEnabled: (enabled: boolean) => void;

   showTimer: boolean;
   setShowTimer: React.Dispatch<React.SetStateAction<boolean>>;

   showDifficulty: boolean;
   setShowDifficulty: React.Dispatch<React.SetStateAction<boolean>>;

   onShuffle: () => void;
}

/**
 * PreferenceSettingsPanel Component
 *
 * A reusable settings panel component that provides user preference controls
 * for the problems page, including toggles for bookmarks, timer, difficulty display,
 * and a shuffle functionality for problem ordering.
 *
 * @param props - Component props containing state and handlers for all settings
 * @returns JSX element containing the preference settings panel
 */
export function PreferenceSettingsPanel({
   bookmarksEnabled,
   setBookmarksEnabled,
   showTimer,
   setShowTimer,
   showDifficulty,
   setShowDifficulty,
   onShuffle,
}: PreferenceSettingsPanelProps) {
   return (
      <section
         className="flex flex-col gap-4 mt-5"
         role="region"
         aria-labelledby="preference-settings-heading"
         data-testid="preference-settings-panel"
      >
         <h1
            id="preference-settings-heading"
            className="text-2xl tracking-tight mb-4"
         >
            {uiStrings.headers.preferenceSettings}
         </h1>

         <div
            className="flex justify-between gap-4 items-center"
            data-testid="bookmarks-setting"
         >
            <label
               htmlFor="bookmarks-switch"
               className="text-foreground cursor-pointer"
            >
               {uiStrings.labels.enableBookmarks}
            </label>
            <Switch
               id="bookmarks-switch"
               checked={bookmarksEnabled}
               onCheckedChange={() => {
                  setBookmarksEnabled(!bookmarksEnabled);
               }}
               aria-label={`${
                  bookmarksEnabled ? "Disable" : "Enable"
               } bookmarks functionality`}
               data-testid="bookmarks-switch"
            />
         </div>

         <div
            className="flex justify-between gap-4 items-center"
            data-testid="timer-setting"
         >
            <label
               htmlFor="timer-switch"
               className="text-foreground cursor-pointer"
            >
               {uiStrings.labels.showTimer}
            </label>
            <Switch
               id="timer-switch"
               checked={showTimer}
               onCheckedChange={() => {
                  setShowTimer(!showTimer);
               }}
               aria-label={`${
                  showTimer ? "Hide" : "Show"
               } timer during questions`}
               data-testid="timer-switch"
            />
         </div>

         <div
            className="flex justify-between gap-4 items-center"
            data-testid="difficulty-setting"
         >
            <label
               htmlFor="difficulty-switch"
               className="text-foreground cursor-pointer"
            >
               {uiStrings.labels.showDifficulty}
            </label>
            <Switch
               id="difficulty-switch"
               checked={showDifficulty}
               onCheckedChange={() => {
                  setShowDifficulty(!showDifficulty);
               }}
               aria-label={`${
                  showDifficulty ? "Hide" : "Show"
               } difficulty level for questions`}
               data-testid="difficulty-switch"
            />
         </div>

         <div
            className="flex justify-between gap-4 items-center"
            data-testid="shuffle-setting"
         >
            <span className="text-foreground">
               {uiStrings.labels.shuffleQuestions}
            </span>
            <button
               onClick={onShuffle}
               className="p-2 rounded-lg bg-primary text-primary-foreground hover:bg-primary/90 transition-colors duration-200 flex items-center gap-2 focus:ring-2 focus:ring-primary/50 focus:outline-none"
               title={uiStrings.messages.shuffleTooltip}
               aria-label="Shuffle the order of questions randomly"
               data-testid="shuffle-button"
            >
               <Shuffle size={16} aria-hidden="true" />
               <span className="text-sm">{uiStrings.buttons.shuffle}</span>
            </button>
         </div>
      </section>
   );
}
