# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

**Development:**
- `pnpm dev` - Start development server
- `pnpm build` - Build for production
- `pnpm start` - Start production server
- `pnpm lint` - Run ESLint

**Database:**
- `pnpm seed` - Seed database with sample data
- `npx prisma migrate dev` - Run database migrations
- `npx prisma generate` - Generate Prisma client
- `npx prisma studio` - Open database browser

## Architecture Overview

CompEx is a competitive exam preparation platform built with Next.js 14 App Router, supporting GRE, GMAT, and CAT exams with interactive question-solving interfaces.

### Core Architecture - Feature-First Organization

CompEx follows a feature-first architecture with clear separation of concerns and organized root directory:

```
/compex/
├── app/ (Next.js App Router - Clean routes and API)
├── features/ (Feature-based organization)
│   ├── exam-management/ (Exam types, sections, tags)
│   ├── question-solving/ (Question display, templates, navigation)
│   ├── user-analytics/ (Performance tracking, timers, results)
│   └── user-management/ (Future: Authentication, profiles)
├── shared/ (Reusable components and utilities)
│   ├── components/ (UI components, layouts, forms)
│   ├── hooks/ (Custom React hooks)
│   ├── lib/ (Utilities, configurations, types)
│   └── styles/ (Global styles and themes)
├── config/ (Configuration files with root symlinks)
│   ├── next.config.mjs
│   ├── tailwind.config.ts
│   ├── tsconfig.json
│   ├── postcss.config.mjs
│   └── components.json
├── docs/ (Documentation and protocols)
│   ├── README.md
│   ├── README.Docker.md
│   ├── MIGRATION_ROADMAP.md
│   └── PROTOCOLS/
├── assets/ (Static assets and images)
├── prisma/ (Database schema and migrations)
└── public/ (Next.js public assets)
```

### Core Data Models

The application centers around a sophisticated question management system:

- **problems**: Individual questions with metadata, options, solutions, and KaTeX support
- **ProblemsSet**: Parent-child question groupings for complex multi-part questions
- **examtypes/sections**: Hierarchical exam structure (GRE -> Quants/Verbal)
- **tags**: Flexible question categorization for filtering
- **userattempts**: Tracks user answers with correctness and timing
- **performance**: User analytics and progress metrics

### Key Architectural Patterns

**Feature-Based Services**: Business logic organized by feature:
- `/features/question-solving/services/` - Question queries and attempt processing
- `/features/exam-management/services/` - Tag and exam type management  
- `/features/user-analytics/hooks/` - Performance tracking and timer logic

**Template-Based Question Rendering**: Question types (DS, GI, TA, RC, CR, NE, MSR) use specific template components in `/features/question-solving/components/QuestionWindow/templates/`

**Shared Component System**: Reusable UI components in `/shared/components/ui/` following shadcn/ui patterns with:
- Consistent variant-based styling using class-variance-authority
- Dark/light theme support
- TypeScript-first design with proper prop interfaces

**State Management**: Zustand stores handle:
- Problem pagination and filtering state (in question-solving feature)
- Question navigation and display
- User attempt tracking
- Theme preferences

### Component Architecture

**Question Solving Feature**:
- Floating window system with navigation (`/features/question-solving/components/QuestionWindow/`)
- Template-based rendering for different question types
- Chart/graph visualization support with Recharts
- Real-time answer validation and submission
- Pagination and navigation components

**Exam Management Feature**:
- Tag-based filtering with multi-select capabilities (`/features/exam-management/components/`)
- Exam type and section selection
- Panel and tab-based organization

**User Analytics Feature**:
- Timer components with settings (`/features/user-analytics/components/timer/`)
- Result window for performance display
- Answer tracking and analytics

**Shared Components**:
- UI primitives: Button, Table, Select, Switch, etc. (`/shared/components/ui/`)
- Layout components: Header, Footer, Flows (`/shared/components/layouts/`)
- Loading and feedback components (`/shared/components/feedback/`)

### Database Relationships

Critical relationships to understand:
- `problems.problemSetId` -> `ProblemsSet.id` (parent-child questions)
- `problems.examTypeId` -> `examtypes.id` (exam categorization)
- `problems.sectionId` -> `sections.id` (section categorization)
- `userattempts.problemId` -> `problems.id` (answer tracking)

### API Structure

RESTful API routes in `/app/api/problems/`:
- `getProblems` - Paginated questions with filtering
- `getQuestions` - Specific question details
- `getTags` - Available filter tags
- `returnAttempt` - Answer submission and validation

### Import Path Structure

- `@/features/[feature-name]/` - Feature-specific code
- `@/shared/` - Shared components, utilities, and configurations
- `@/app/` - Next.js App Router pages and API routes

### Development Notes

- Math expressions use KaTeX rendering
- Questions support complex formatting with HTML and mathematical notation
- Performance metrics calculate streaks, accuracy, and section-wise analytics
- Docker-based PostgreSQL database (see docs/README.Docker.md)
- TypeScript strict mode enabled with comprehensive type definitions
- All UI components follow shadcn/ui patterns with consistent theming