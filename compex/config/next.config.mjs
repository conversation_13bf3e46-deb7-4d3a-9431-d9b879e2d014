import bundleAnalyzer from '@next/bundle-analyzer';

const withBundleAnalyzer = bundleAnalyzer({
   enabled: process.env.ANALYZE === 'true',
});

/** @type {import('next').NextConfig} */
const nextConfig = {
   reactStrictMode: true,
   eslint: {
      // Warning: This allows production builds to successfully complete even if
      // your project has ESLint errors.
      ignoreDuringBuilds: true,
   },
   experimental: {
      // Enable performance insights
      webVitalsAttribution: ['CLS', 'LCP', 'FCP', 'FID', 'TTFB'],
   },
   // Optimize bundle splitting
   webpack: (config, { isServer, dev }) => {
      // Production optimizations
      if (!dev && !isServer) {
         config.optimization = {
            ...config.optimization,
            splitChunks: {
               chunks: 'all',
               cacheGroups: {
                  // Vendor chunks for better caching
                  vendor: {
                     test: /[\\/]node_modules[\\/]/,
                     name: 'vendors',
                     chunks: 'all',
                     priority: 10,
                  },
                  // Common chunks between pages
                  common: {
                     minChunks: 2,
                     chunks: 'all',
                     name: 'common',
                     priority: 5,
                  },
                  // UI components chunk
                  ui: {
                     test: /[\\/]shared[\\/]components[\\/]ui[\\/]/,
                     name: 'ui-components',
                     chunks: 'all',
                     priority: 15,
                  },
                  // Feature-specific chunks
                  features: {
                     test: /[\\/]features[\\/]/,
                     name: 'features',
                     chunks: 'all',
                     priority: 8,
                  },
               },
            },
         };

         // Add bundle size budgets (warnings)
         config.performance = {
            ...config.performance,
            maxEntrypointSize: 512000, // 500KB
            maxAssetSize: 512000, // 500KB
            hints: process.env.NODE_ENV === 'production' ? 'warning' : false,
         };
      }

      return config;
   },
   // Image optimization
   images: {
      formats: ['image/webp', 'image/avif'],
      minimumCacheTTL: 60 * 60 * 24 * 30, // 30 days
   },
   // Compression
   compress: true,
   // Power optimizations for production
   poweredByHeader: false,
   generateEtags: false,
};

export default withBundleAnalyzer(nextConfig);
